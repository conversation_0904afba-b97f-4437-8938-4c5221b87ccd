use FormatID::*;
use once_cell::sync::Lazy;
use std::{sync::RwLock, fmt};

static S_BY_ALPHA_THRESHOLD: Lazy<RwLock<u8>> = Lazy::new(|| RwLock::new(0x80));
static S_B_VERBOSE: Lazy<RwLock<bool>> = Lazy::new(|| RwLock::new(false));
static S_B_NO_MIPS: Lazy<RwLock<bool>> = Lazy::new(|| RwLock::new(false));
static S_F_GAMMA_FACTOR: Lazy<RwLock<f32>> = Lazy::new(|| RwLock::new(1.0));
static S_T_TARGET_FORMAT: Lazy<RwLock<FormatID>> = Lazy::new(|| RwLock::new(Unspecified));

pub fn set_alpha_threshold(value: u8) {
    *S_BY_ALPHA_THRESHOLD.write().unwrap() = value;
}

pub fn get_alpha_threshold() -> u8 {
    *S_BY_ALPHA_THRESHOLD.read().unwrap()
}
pub fn set_verbose(value: bool) {
    *S_B_VERBOSE.write().unwrap() = value;
}

pub fn get_verbose() -> bool {
    *S_B_VERBOSE.read().unwrap()
}

pub fn set_no_mips(value: bool) {
    *S_B_NO_MIPS.write().unwrap() = value;
}

pub fn get_no_mips() -> bool {
    *S_B_NO_MIPS.read().unwrap()
}

pub fn get_gamma_factor() -> f32 {
    *S_F_GAMMA_FACTOR.read().unwrap()
}

pub fn get_target_format() -> FormatID {
    *S_T_TARGET_FORMAT.read().unwrap()
}

pub fn set_target_format(format: FormatID) {
    *S_T_TARGET_FORMAT.write().unwrap() = format;
}

#[repr(u8)]
#[derive(Clone, Copy, PartialEq, PartialOrd)]
pub enum FormatID {
    Unspecified = 0,
    BlpPalA0,
    BlpPalA1,
    BlpPalA4,
    BlpPalA8,
    BlpDxt1A0,
    BlpDxt1A1,
    BlpDxt3,
    BlpDxt5,
    BlpBgra,
    PngPal,
    PngPalMask,
    PngRgb,
    PngRgba,
    FormatCount,
}

pub const FORMAT_INFO: &[(&'static str, &'static str)] = &[
    ("Unspecified", "[INVALID]"),
    ("BlpPalA0", "Palettized (no alpha)"),
    ("BlpPalA1", "Palettized (1-bit alpha)"),
    ("BlpPalA4", "Palettized (4-bit alpha)"),
    ("BlpPalA8", "Palettized (full alpha)"),
    ("BlpDxt1A0", "Dxt1 (no alpha)"),
    ("BlpDxt1A1", "Dxt1 (alpha)"),
    ("BlpDxt3", "Dxt3"),
    ("BlpDxt5", "Dxt5"),
    ("BlpBgra", "Bgra"),
    ("PngPal", "Palettized (no alpha)"),
    ("PngPalMask", "Palettized (with transparency)"),
    ("PngRgb", "Rgb"),
    ("PngRgba", "Rgba"),
];

impl FormatID {
    pub fn is_blp(&self) -> bool {
        matches!(*self as u8, 1..=9)
    }

    pub fn is_png(&self) -> bool {
        matches!(*self as u8, 10..=13)
    }

    pub fn name(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(name, _)| name)
            .unwrap_or_default()
    }

    pub fn description(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(_, description)| description)
            .unwrap_or_default()
    }
}

impl From<usize> for FormatID {
    fn from(value: usize) -> Self {
        match value {
            1 => BlpPalA0,
            2 => BlpPalA1,
            3 => BlpPalA4,
            4 => BlpPalA8,
            5 => BlpDxt1A0,
            6 => BlpDxt1A1,
            7 => BlpDxt3,
            8 => BlpDxt5,
            9 => BlpBgra,
            10 => PngPal,
            11 => PngPalMask,
            12 => PngRgb,
            13 => PngRgba,
            14 => FormatCount,
            _ => Unspecified, // 默认 fallback
        }
    }
}

pub static FORMAT_RULES: Lazy<RwLock<Vec<(FormatID, FormatID)>>> = Lazy::new(|| {
    RwLock::new(vec![
        (Unspecified, Unspecified),
        (BlpPalA0, PngPal),
        (BlpPalA1, PngPalMask),
        (BlpPalA4, PngRgba),
        (BlpPalA8, PngRgba),
        (BlpDxt1A0, PngRgb),
        (BlpDxt1A1, PngRgba),
        (BlpDxt3, PngRgba),
        (BlpDxt5, PngRgba),
        (BlpBgra, PngRgba),
        (PngPal, BlpPalA0),
        (PngPalMask, BlpPalA1),
        (PngRgb, BlpDxt1A0),
        (PngRgba, BlpDxt3),
    ])
});

pub fn mapped_format(src: FormatID) -> FormatID {
    FORMAT_RULES
        .read()
        .unwrap()
        .iter()
        .find(|&&(from, _)| from == src)
        .map(|&(_, to)| to)
        .unwrap_or(Unspecified)
}

pub fn set_mapping(from: FormatID, to: FormatID) {
    let mut rules = FORMAT_RULES.write().unwrap();
    if let Some(pair) = rules.iter_mut().find(|(f, _)| *f == from) {
        pair.1 = to;
    } else {
        rules.push((from, to));
    }
}

#[derive(Debug)]
pub enum AppError {
    InvalidArgs,
    InvalidAlphaThreshold,
    InvalidFormatString,
    InvalidConversionRule,
    ExclusiveOptions,
    FilenameMissing,
    ProcessingFailed(std::path::PathBuf),
    GlobError(String), // For errors from the glob crate
    FileError(std::io::Error),
    NotBlp,
    VersionError,
    Error(&'static str),
    Patch,
    WriteError(png::EncodingError),
    ReadError(png::DecodingError),
    LiqError(imagequant::liq_error),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::FileError(e) => write!(f, "File error: {}", e),
            AppError::NotBlp => write!(f, "Not a valid BLP file."),
            AppError::VersionError => write!(f, "Unsupported version"),
            AppError::Error(msg) => write!(f, "Error: {}", msg),
            AppError::Patch => write!(f, "Patch error"),
            AppError::WriteError(e) => write!(f, "Encodding error: {}", e),
            AppError::ReadError(e) => write!(f, "Decoding error: {}", e),
            AppError::LiqError(e) => write!(f, "Image quant fail: {}", e),
            AppError::InvalidArgs => write!(f, "Invalid arguments."),
            AppError::InvalidAlphaThreshold => write!(f, "ERROR: Alpha threshold must be between 0 and 255."),
            AppError::InvalidFormatString => write!(f, "ERROR: Invalid format string provided."),
            AppError::InvalidConversionRule => write!(f, "ERROR: Invalid conversion rule format. Expected SOURCE_FORMAT=TARGET_FORMAT."),
            AppError::ExclusiveOptions => write!(f, "ERROR: -r and -h are exclusive."),
            AppError::FilenameMissing => write!(f, "ERROR: Filename argument missing."),
            AppError::ProcessingFailed(path) => write!(f, "ERROR: Processing of '{}' failed.", path.display()),
            AppError::GlobError(msg) => write!(f, "ERROR: Glob pattern error: {}.", msg),
        }
    }
}

impl std::error::Error for AppError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            AppError::FileError(e) => Some(e),
            AppError::WriteError(e) => Some(e),
            AppError::ReadError(e) => Some(e),
            AppError::LiqError(e) => Some(e),
            _ => None,
        }
    }
}

impl From<std::io::Error> for AppError {
    fn from(e: std::io::Error) -> Self {
        AppError::FileError(e)
    }
}

impl From<png::EncodingError> for AppError {
    fn from(e: png::EncodingError) -> Self {
        AppError::WriteError(e)
    }
}

impl From<png::DecodingError> for AppError {
    fn from(e: png::DecodingError) -> Self {
        AppError::ReadError(e)
    }
}

impl From<imagequant::liq_error> for AppError {
    fn from(e: imagequant::liq_error) -> Self {
        AppError::LiqError(e)
    }
}

impl From<glob::GlobError> for AppError {
    fn from(err: glob::GlobError) -> Self {
        AppError::GlobError(err.to_string())
    }
}

impl From<glob::PatternError> for AppError {
    fn from(err: glob::PatternError) -> Self {
        AppError::GlobError(err.to_string())
    }
}