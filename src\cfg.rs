// 核心类型定义 - 简化版cfg模块
use std::fmt;
use FormatID::*;

#[repr(u8)]
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, PartialOrd)]
pub enum FormatID {
    Unspecified = 0,
    BlpPalA0,
    BlpPalA1,
    BlpPalA4,
    BlpPalA8,
    BlpDxt1A0,
    BlpDxt1A1,
    BlpDxt3,
    BlpDxt5,
    BlpBgra,
    PngPal,
    PngPalMask,
    PngRgb,
    PngRgba,
    FormatCount,
}

pub const FORMAT_INFO: &[(&'static str, &'static str)] = &[
    ("Unspecified", "[INVALID]"),
    ("BlpPalA0", "Palettized (no alpha)"),
    ("BlpPalA1", "Palettized (1-bit alpha)"),
    ("BlpPalA4", "Palettized (4-bit alpha)"),
    ("BlpPalA8", "Palettized (full alpha)"),
    ("BlpDxt1A0", "Dxt1 (no alpha)"),
    ("BlpDxt1A1", "Dxt1 (alpha)"),
    ("BlpDxt3", "Dxt3"),
    ("BlpDxt5", "Dxt5"),
    ("BlpBgra", "Bgra"),
    ("PngPal", "Palettized (no alpha)"),
    ("PngPalMask", "Palettized (with transparency)"),
    ("PngRgb", "Rgb"),
    ("PngRgba", "Rgba"),
];

impl FormatID {
    pub fn is_blp(&self) -> bool {
        matches!(*self as u8, 1..=9)
    }

    pub fn is_png(&self) -> bool {
        matches!(*self as u8, 10..=13)
    }

    pub fn name(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(name, _)| name)
            .unwrap_or_default()
    }

    pub fn description(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(_, description)| description)
            .unwrap_or_default()
    }
}

impl From<usize> for FormatID {
    fn from(value: usize) -> Self {
        match value {
            1 => BlpPalA0,
            2 => BlpPalA1,
            3 => BlpPalA4,
            4 => BlpPalA8,
            5 => BlpDxt1A0,
            6 => BlpDxt1A1,
            7 => BlpDxt3,
            8 => BlpDxt5,
            9 => BlpBgra,
            10 => PngPal,
            11 => PngPalMask,
            12 => PngRgb,
            13 => PngRgba,
            14 => FormatCount,
            _ => Unspecified, // 默认 fallback
        }
    }
}

// 默认格式映射规则（用于显示）
pub static FORMAT_RULES: &[(FormatID, FormatID)] = &[
    (FormatID::Unspecified, FormatID::Unspecified),
    (FormatID::BlpPalA0, FormatID::PngPal),
    (FormatID::BlpPalA1, FormatID::PngPalMask),
    (FormatID::BlpPalA4, FormatID::PngRgba),
    (FormatID::BlpPalA8, FormatID::PngRgba),
    (FormatID::BlpDxt1A0, FormatID::PngRgb),
    (FormatID::BlpDxt1A1, FormatID::PngRgba),
    (FormatID::BlpDxt3, FormatID::PngRgba),
    (FormatID::BlpDxt5, FormatID::PngRgba),
    (FormatID::BlpBgra, FormatID::PngRgba),
    (FormatID::PngPal, FormatID::BlpPalA0),
    (FormatID::PngPalMask, FormatID::BlpPalA1),
    (FormatID::PngRgb, FormatID::BlpDxt1A0),
    (FormatID::PngRgba, FormatID::BlpDxt3),
];

#[derive(Debug)]
pub enum AppError {
    InvalidArgs,
    InvalidAlphaThreshold,
    InvalidFormatString,
    InvalidConversionRule,
    ExclusiveOptions,
    FilenameMissing,
    ProcessingFailed(std::path::PathBuf),
    GlobError(String), // For errors from the glob crate
    FileError(std::io::Error),
    NotBlp,
    VersionError,
    Error(&'static str),
    Patch,
    WriteError(png::EncodingError),
    ReadError(png::DecodingError),
    LiqError(imagequant::liq_error),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::FileError(e) => write!(f, "File error: {}", e),
            AppError::NotBlp => write!(f, "Not a valid BLP file."),
            AppError::VersionError => write!(f, "Unsupported version"),
            AppError::Error(msg) => write!(f, "Error: {}", msg),
            AppError::Patch => write!(f, "Patch error"),
            AppError::WriteError(e) => write!(f, "Encodding error: {}", e),
            AppError::ReadError(e) => write!(f, "Decoding error: {}", e),
            AppError::LiqError(e) => write!(f, "Image quant fail: {}", e),
            AppError::InvalidArgs => write!(f, "Invalid arguments."),
            AppError::InvalidAlphaThreshold => write!(f, "ERROR: Alpha threshold must be between 0 and 255."),
            AppError::InvalidFormatString => write!(f, "ERROR: Invalid format string provided."),
            AppError::InvalidConversionRule => write!(f, "ERROR: Invalid conversion rule format. Expected SOURCE_FORMAT=TARGET_FORMAT."),
            AppError::ExclusiveOptions => write!(f, "ERROR: -r and -h are exclusive."),
            AppError::FilenameMissing => write!(f, "ERROR: Filename argument missing."),
            AppError::ProcessingFailed(path) => write!(f, "ERROR: Processing of '{}' failed.", path.display()),
            AppError::GlobError(msg) => write!(f, "ERROR: Glob pattern error: {}.", msg),
        }
    }
}

impl std::error::Error for AppError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            AppError::FileError(e) => Some(e),
            AppError::WriteError(e) => Some(e),
            AppError::ReadError(e) => Some(e),
            AppError::LiqError(e) => Some(e),
            _ => None,
        }
    }
}

impl From<std::io::Error> for AppError {
    fn from(e: std::io::Error) -> Self {
        AppError::FileError(e)
    }
}

impl From<png::EncodingError> for AppError {
    fn from(e: png::EncodingError) -> Self {
        AppError::WriteError(e)
    }
}

impl From<png::DecodingError> for AppError {
    fn from(e: png::DecodingError) -> Self {
        AppError::ReadError(e)
    }
}

impl From<imagequant::liq_error> for AppError {
    fn from(e: imagequant::liq_error) -> Self {
        AppError::LiqError(e)
    }
}

impl From<glob::GlobError> for AppError {
    fn from(err: glob::GlobError) -> Self {
        AppError::GlobError(err.to_string())
    }
}

impl From<glob::PatternError> for AppError {
    fn from(err: glob::PatternError) -> Self {
        AppError::GlobError(err.to_string())
    }
}