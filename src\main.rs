// 重构后的主程序 - 使用新的架构
mod img;
mod blp;
mod png;
mod squish;
mod config;
mod image;
mod pipeline;

use std::io::{self, Write};

use crate::config::{Config, AppError, FORMAT_INFO, FORMAT_RULES};
use crate::pipeline::ProcessingPipeline;

fn list_formats() {
    println!("****************");
    println!("* File Formats *");
    println!("****************");
    println!("Format Name\tDescription");
    println!("________________________________________________");
    for &(name, description) in FORMAT_INFO.iter().skip(1) {
        println!("{}  \t {}", name, description);
    }
    println!("\n**************************");
    println!("* Conversion Rules Table *");
    println!("**************************");
    println!("Source Format\t Target Format");
    println!("________________________________________________");
    for (source, target) in FORMAT_RULES.iter().skip(1) {
        println!("{:<10}  ->  {}", source.name(), target.name());
    }
    println!("\nThe Conversion Rules table shows the format given to the destination file");
    println!("when the source file has a given format. You can change a rule with the /U");
    println!("option, or you can force the destination file into a given format with /F.");
    println!("/U can be specified multiple times. Blp->Blp and Png->Png is OK.");
    println!("\nExamples:");
    println!("  blpconverter /FPngRgb myfile.blp");
    println!("  blpconverter /UBlpPalA0=PngRgb /UPngPal=PngRgb myfile.blp\n");
}

fn usage() {
    const INDENT: &str = "\n            ";
    
    println!("\nBLP2PNG: Converts BLP files to PNGs and vice versa.");
    println!("Version 2.0 (C) 2025 Andy Zih (<EMAIL>)");
    println!("This program is free software under the GNU General Public License.");
    println!("BLPCONVERTER [options] sourceFile [targetFile | sourceFile [...]]");
    println!("sourceFile The file to convert.");
    println!("targetFile Optionally, the name of the converted file. If omitted, target{}file is given the same name as sourceFile but with the opposite{}extension.", INDENT, INDENT);
    println!("/A(value)   Sets the Alpha threshold when converting from palettized, 8-bit{}BLPs to palettized PNGs. Value is a number between 0 and 255.{}Source alpha values below the threshold are fully transparent, above{}are fully opaque. Default is {}.", INDENT, INDENT, INDENT, 0x80);
    println!("/C          Create mip test image. Outputs an image which contains all of the{}generated mip levels.", INDENT);
    println!("/E          Pause on Error. (Handy for drag-and-drop use.)");
    println!("/F(format)  Forces target Format. Overrides all other settings, including{}targetFile extension.", INDENT);
    println!("/H          Force WoW cHaracter texture format (palettized, no alpha) when{}making BLPs.", INDENT);
    println!("/I          Info mode. Only outputs file information. This option{}automatically sets the /V and /M options.", INDENT);
    println!("/L          Lists formats and conversion rules.");
    println!("/M          Multi-file mode. In this mode, multiple files can be input after{}options. It is not possible to specify custom output names for them{}in this mode.", INDENT, INDENT);
    println!("/N          No mips. Disables creation of mip levels when saving BLPs.");
    println!("/P          Pause upon completion. (Handy for drag-and-drop use.)");
    println!("/R          Force WoW clothing texture formats. All created BLPs are palettized{}and all PNGs are RGB/RGBA.", INDENT);
    println!("/U(format)=(format){}Change conversion rUle. See /L.", INDENT);
    println!("/V          Verbose mode. Outputs additional information.");
}

fn run_with_pipeline(config: &Config) -> Result<(), AppError> {
    // 创建处理管道
    let pipeline = ProcessingPipeline::new(config.clone());
    
    if config.multi_file_mode {
        // 批量处理模式
        let patterns: Vec<String> = config.source_files.iter()
            .map(|p| p.to_string_lossy().to_string())
            .collect();
        
        let results = pipeline.process_batch(&patterns)?;
        
        // 显示处理结果统计
        if config.verbose {
            println!("\n=== Processing Summary ===");
            println!("Total files processed: {}", results.len());
            
            let total_time: std::time::Duration = results.iter()
                .map(|r| r.processing_time)
                .sum();
            
            let total_size_before: u64 = results.iter()
                .map(|r| r.file_size_before)
                .sum();
            
            let total_size_after: u64 = results.iter()
                .map(|r| r.file_size_after)
                .sum();
            
            println!("Total processing time: {:?}", total_time);
            println!("Total size before: {} bytes", total_size_before);
            println!("Total size after: {} bytes", total_size_after);
            
            if total_size_before > 0 {
                let compression_ratio = (total_size_after as f64 / total_size_before as f64) * 100.0;
                println!("Size ratio: {:.1}%", compression_ratio);
            }
        }
    } else {
        // 单文件处理模式
        let source = &config.source_files[0];
        let dest = config.dest_file.as_ref().map(|s| s.as_path());
        
        let result = pipeline.process_file(source, dest)?;
        
        if config.verbose {
            println!("Processing completed in {:?}", result.processing_time);
            println!("File size: {} -> {} bytes", result.file_size_before, result.file_size_after);
        }
    }
    
    Ok(())
}

fn main() -> Result<(), AppError> {
    // 解析配置
    let config = match Config::from_args() {
        Ok(config) => config,
        Err(AppError::Error("Help requested")) => {
            usage();
            return Ok(());
        }
        Err(AppError::InvalidArgs) => {
            usage();
            return Ok(());
        }
        Err(AppError::ListFormatsRequested) => {
            list_formats();
            return Ok(());
        }
        Err(e) => {
            eprintln!("{}", e.to_string());
            return Err(e);
        }
    };
    
    // 处理特殊命令
    if config.source_files.iter().any(|f| f.to_string_lossy() == "/L" || f.to_string_lossy() == "-L") {
        list_formats();
        return Ok(());
    }
    
    // 应用配置到全局状态（临时兼容性措施）
    apply_config_to_globals(&config);
    
    // 运行处理管道
    let result = run_with_pipeline(&config);
    
    // 处理暂停选项
    let should_pause = config.pause_at_end
        || (config.pause_on_error && result.is_err());
    
    if should_pause {
        println!("Press Enter to continue...");
        let _ = io::stdout().flush();
        let mut buffer = String::new();
        let _ = io::stdin().read_line(&mut buffer).map_err(AppError::from)?;
    }
    
    result
}

// 临时函数：将新配置应用到旧的全局状态
// 这是为了保持向后兼容性，最终会被移除
fn apply_config_to_globals(_config: &Config) {
    // 注意：全局状态函数已被移除，新架构不再需要全局状态
    // 这个函数保留只是为了兼容性，实际上什么都不做
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    
    #[test]
    fn test_config_creation() {
        let config = Config::default();
        assert_eq!(config.alpha_threshold, 0x80);
        assert!(!config.verbose);
        assert!(!config.no_mips);
    }
    
    #[test]
    fn test_pipeline_creation() {
        let config = Config::default();
        let _pipeline = ProcessingPipeline::new(config);
        // 如果能创建管道，说明基本架构正确
    }
    
    #[test]
    fn test_format_registry() {
        use crate::image::FormatRegistry;
        
        let registry = FormatRegistry::new();
        let extensions = registry.supported_extensions();
        
        assert!(extensions.contains(&"blp".to_string()));
        assert!(extensions.contains(&"png".to_string()));
    }
}
