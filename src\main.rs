mod img;
mod cfg;
mod blp;
mod png;
mod squish;

use std::{
    env,
    io::{self, Write}, // For `stdout().flush()` and `read_line()`
    path::Path,
};

use crate::img::MemImage;
use crate::cfg::*;

fn process_file(
    input_path: &str,
    destination_filename: Option<&Path>,
) -> Result<(), AppError> {
    let input_path = Path::new(input_path);
    let filename_stem = input_path.file_stem().and_then(|s| s.to_str())
        .ok_or_else(|| AppError::ProcessingFailed(input_path.to_path_buf()))?;

    let extension = input_path.extension().and_then(|s| s.to_str())
        .ok_or_else(|| AppError::ProcessingFailed(input_path.to_path_buf()))?
        .to_lowercase();

    let mut image = MemImage::default();
    let input_format;

    if extension == "blp" {
        input_format = image.load_from_blp(input_path)?;
    } else if extension == "png" {
        input_format = image.load_from_png(input_path)?;
    } else {
        return Err(AppError::Error("Input file is not PNG or BLP"));
    }

    if get_verbose() {
        return Ok(());
    }

    let mut target_format = get_target_format();
    if target_format == FormatID::Unspecified {
        target_format = mapped_format(input_format);
    }

    let target_path = if let Some(dest) = destination_filename {
        dest.to_path_buf()
    } else {
        let same_type = (input_format.is_blp() && target_format.is_blp())
            || (input_format.is_png() && target_format.is_png());

        let ext = if target_format.is_blp() { "blp" } else { "png" };
        let suffix = if same_type { "_" } else { "" };

        input_path.with_file_name(format!("{}{}.{ext}", filename_stem, suffix))
    };

    println!(
        "Converting: {} ({}) -> {} ({})",
        input_path.display(),
        input_format.name(),
        target_path.display(),
        target_format.name(),
    );

    image.save(&target_path, target_format)?;

    Ok(())
}

fn list_formats() {
    println!("****************");
    println!("* File Formats *");
    println!("****************");
    println!("Format Name\tDescription");
    println!("________________________________________________");
    for &(name, description) in FORMAT_INFO.iter().skip(1) {
        println!("{}  \t {}", name, description);
    }
    println!("\n**************************");
    println!("* Conversion Rules Table *");
    println!("**************************");
    println!("Source Format\t Target Format");
    println!("________________________________________________");
    {
        let rules = FORMAT_RULES.read().unwrap();
        for (source, target) in rules.iter().skip(1) {
            println!("{:<10}  ->  {}", source.name(), target.name());
        }
    }
    println!("\nThe Conversion Rules table shows the format given to the destination file");
    println!("when the source file has a given format. You can change a rule with the /U");
    println!("option, or you can force the destination file into a given format with /F.");
    println!("/U can be specified multiple times. Blp->Blp and Png->Png is OK.");
    println!("\nExamples:");
    println!("  blpconverter /FPngRgb myfile.blp");
    println!("  blpconverter /UBlpPalA0=PngRgb /UPngPal=PngRgb myfile.blp\n");
}

fn usage() {
    // Define the indent string as a constant
    const INDENT: &str = "\n            "; // Note: 8 spaces
    
    println!("\nBLP2PNG: Converts BLP files to PNGs and vice versa.");
    println!("Version 1.0 (C) 2025 Andy Zih (<EMAIL>)");
    println!("This program is free software under the GNU General Public License.");
    println!("BLPCONVERTER [options] sourceFile [targetFile | sourceFile [...]]");
    println!("sourceFile The file to convert.");
    println!("targetFile Optionally, the name of the converted file. If omitted, target{}file is given the same name as sourceFile but with the opposite{}extension.", INDENT, INDENT);
    println!("/A(value)   Sets the Alpha threshold when converting from palettized, 8-bit{}BLPs to palettized PNGs. Value is a number between 0 and 255.{}Source alpha values below the threshold are fully transparent, above{}are fully opaque. Default is {}.", INDENT, INDENT, INDENT, get_alpha_threshold());
    println!("/C          Create mip test image. Outputs an image which contains all of the{}generated mip levels.", INDENT);
    println!("/E          Pause on Error. (Handy for drag-and-drop use.)");
    println!("/F(format)  Forces target Format. Overrides all other settings, including{}targetFile extension.", INDENT);
    // The commented-out C code is also commented out here:
    // LOG("-g(factor) : (PNG->BLP only) Applies a Gamma factor to the entire image.\n   (factor) is a number between 0.0 and 1.0.  If source texture is PNG RGB,\n   an alpha channel will be added.\n");
    println!("/H          Force WoW cHaracter texture format (palettized, no alpha) when{}making BLPs.", INDENT);
    println!("/I          Info mode. Only outputs file information. This option{}automatically sets the /V and /M options.", INDENT);
    println!("/L          Lists formats and conversion rules.");
    println!("/M          Multi-file mode. In this mode, multiple files can be input after{}options. It is not possible to specify custom output names for them{}in this mode.", INDENT, INDENT);
    println!("/N          No mips. Disables creation of mip levels when saving BLPs.");
    println!("/P          Pause upon completion. (Handy for drag-and-drop use.)");
    println!("/R          Force WoW clothing texture formats. All created BLPs are palettized{}and all PNGs are RGB/RGBA.", INDENT);
    // The commented-out C code is also commented out here:
    // LOG("-s : Don't preserve alpha.  With this mode, palettized BLPs will always make\n     palettized PNGs (and vice versa).  Note: Alpha channels can be lost with this option.\n");
    println!("/U(format)=(format){}Change conversion rUle. See /L.", INDENT);
    println!("/V          Verbose mode. Outputs additional information.");
}

fn get_format_from_string(s: &str) -> FormatID {
    FORMAT_INFO
        .iter()
        .position(|&(name, _)| name.eq_ignore_ascii_case(s))
        .map(FormatID::from)
        .unwrap_or(FormatID::Unspecified)
}

struct Config {
    multi_file_mode: bool,
    pause_at_end: bool,
    pause_on_error: bool,
    clothing_option_set: bool,
    character_option_set: bool,
    source_files: Vec<String>,
    dest_file: Option<String>,
}

impl Config {
    fn parse_args() -> Result<Self, AppError> {
        let args: Vec<String> = env::args().collect();
        let argc = args.len();

        if argc == 1 {
            usage();
            return Err(AppError::InvalidArgs);
        }

        let mut config = Config {
            multi_file_mode: false,
            pause_at_end: false,
            pause_on_error: false,
            clothing_option_set: false,
            character_option_set: false,
            source_files: Vec::new(),
            dest_file: None,
        };

        let mut i_arg = 1;
        while i_arg < argc {
            let arg_str = &args[i_arg];
            if arg_str.starts_with('-') || arg_str.starts_with('/') {
                let option_char = arg_str.chars().nth(1).unwrap_or('\0').to_ascii_lowercase();
                let opt = &arg_str[2..];
                match option_char {
                    '?' => {
                        usage();
                        std::process::exit(0);
                    }
                    'a' => {
                        let option_value = opt.parse::<u8>().map_err(|_| AppError::InvalidAlphaThreshold)?;
                        set_alpha_threshold(option_value);
                    }
                    'e' => config.pause_on_error = true,
                    'f' => {
                        let format = get_format_from_string(opt);
                        if format == FormatID::Unspecified {
                            list_formats();
                            return Err(AppError::InvalidFormatString);
                        }
                        set_target_format(format);
                    }
                    'h' => {
                        config.character_option_set = true;
                        set_mapping(FormatID::PngPalMask, FormatID::BlpPalA0);
                        set_mapping(FormatID::PngRgb, FormatID::BlpPalA0);
                        set_mapping(FormatID::PngRgba, FormatID::BlpPalA0);
                    }
                    'i' => {
                        println!("File Info Mode.");
                        set_verbose(true);
                        config.multi_file_mode = true;
                    }
                    'l' => {
                        list_formats();
                        std::process::exit(0);
                    }
                    'm' => config.multi_file_mode = true,
                    'n' => set_no_mips(true),
                    'p' => config.pause_at_end = true,
                    'r' => {
                        config.clothing_option_set = true;
                        set_mapping(FormatID::PngRgb, FormatID::BlpPalA0);
                        set_mapping(FormatID::PngRgba, FormatID::BlpPalA8);
                        set_mapping(FormatID::BlpPalA0, FormatID::PngRgb);
                        set_mapping(FormatID::BlpPalA1, FormatID::PngRgba);
                        set_mapping(FormatID::BlpPalA4, FormatID::PngRgba);
                        set_mapping(FormatID::BlpPalA8, FormatID::PngRgba);
                    }
                    'u' => {
                        let parts: Vec<&str> = opt.splitn(2, '=').collect();
                        if parts.len() != 2 {
                            return Err(AppError::InvalidConversionRule);
                        }
                        let src = get_format_from_string(parts[0]);
                        let dest = get_format_from_string(parts[1]);
                        if src == FormatID::Unspecified {
                            list_formats();
                            return Err(AppError::InvalidConversionRule);
                        }
                        set_mapping(src, dest);
                    }
                    'v' => set_verbose(true),
                    _ => {
                        eprintln!("ERROR: -{} is not a valid option.", option_char);
                        return Err(AppError::InvalidArgs);
                    }
                }
            } else {
                break;
            }
            i_arg += 1;
        }

        if config.clothing_option_set && config.character_option_set {
            return Err(AppError::ExclusiveOptions);
        }

        if i_arg == argc {
            return Err(AppError::FilenameMissing);
        }

        for idx in i_arg..argc {
            config.source_files.push(args[idx].clone());
        }

        if !config.multi_file_mode && config.source_files.len() > 1 {
            config.dest_file = config.source_files.pop();
        }

        Ok(config)
    }
}

fn run(config: &Config) -> Result<(), AppError> {
    if config.multi_file_mode {
        for pattern in &config.source_files {
            for entry in glob::glob(pattern)? {
                let path = entry?;
                if path.is_file() {
                    process_file(path.to_str().unwrap(), None)?;
                }
            }
        }
    } else {
        let source = &config.source_files[0];
        let dest = config.dest_file.as_ref().map(|s| Path::new(s));
        process_file(source, dest)?;
    }
    Ok(())
}

fn main() -> Result<(), AppError> {
    let config = Config::parse_args()?;
    let result = run(&config);

    let should_pause = config.pause_at_end
        || (config.pause_on_error && result.is_err());

    if should_pause {
        println!("Press Enter to continue...");
        let _ = io::stdout().flush();
        let mut buffer = String::new();
        let _ = io::stdin().read_line(&mut buffer)?;
    }

    result
}