// squish_wrapper.cpp
#include "squish.h"

extern "C" {
    // int rsq_GetStorageRequirements( int width, int height, int flags ) {
    //     int size = squish::GetStorageRequirements(width, height, flags );
    //     return size;
    // }

    void rsq_compress_image(const unsigned char* rgba, int width, int height, void* blocks, int flags) {
        squish::CompressImage(rgba, width, height, blocks, flags);
    }

    void rsq_decompress_image(unsigned char* rgba, int width, int height, const void* blocks, int flags) {
        squish::DecompressImage(rgba, width, height, blocks, flags);
    }
}