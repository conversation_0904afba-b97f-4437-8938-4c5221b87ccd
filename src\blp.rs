use crate::cfg::{FormatID::*, *};
use crate::img::MemImage;
use crate::squish;
use std::fs::File;
use std::io::{BufWriter, Read, Write};
use std::path::Path;

const ENCODING_STRINGS: [&str; 4] = ["[Invalid]", "Palettized", "Compressed", "BGRA"];

#[derive(PartialEq)]
enum BlpEncoding {
    Invalid = 0,
    Palettized,
    Compressed,
    Bgra,
}

impl From<u8> for BlpEncoding {
    fn from(v: u8) -> Self {
        match v {
            1 => BlpEncoding::Palettized,
            2 => BlpEncoding::Compressed,
            3 => BlpEncoding::Bgra,
            _ => BlpEncoding::Invalid,
        }
    }
}

#[repr(C)]
#[derive(Copy, <PERSON>lone, bytemuck::Pod, bytemuck::Zeroable)]
struct BlpHeader {
    id: [u8; 4],
    version: u32,
    encoding: u8,        // 1 = palettized, 2 = DXT, 3 = raw BGRA?
    alpha_bit_depth: u8, // so far, 0, 1, 4, or 8
    alpha_encoding: u8,  // 7 indicates DXT5, 1 is DXT3.
    has_mips: u8,        // 0 = no mips, 1 = has mips
    x_resolution: u32,
    y_resolution: u32,
    mip_offsets: [u32; 16],
    mip_sizes: [u32; 16],
}

struct BlpFile {
    header: BlpHeader,
    palette: [u8; 4 * 256],
    mips: Vec<Option<Vec<u8>>>,
}

impl BlpFile {
    fn new() -> Self {
        Self {
            header: BlpHeader {
                id: *b"BLP2",
                version: 1,
                encoding: 0,
                alpha_bit_depth: 0,
                alpha_encoding: 0,
                has_mips: 0,
                x_resolution: 0,
                y_resolution: 0,
                mip_offsets: [0; 16],
                mip_sizes: [0; 16],
            },
            palette: [0; 4 * 256],
            mips: vec![None], 
        }
    }

    fn set_palette(&mut self, palette: &Vec<[u8; 3]>) {
        for i in 0..256 {
            self.palette[i * 4 + 0] = palette[i][2];
            self.palette[i * 4 + 1] = palette[i][1];
            self.palette[i * 4 + 2] = palette[i][0];
            self.palette[i * 4 + 3] = 0;
        }
    }

    fn save<P: AsRef<Path>>(self, filename: P) -> Result<(), AppError> {
        let file = File::create(&filename)?;
        let ref mut writer = BufWriter::new(file);

        writer.write_all(bytemuck::bytes_of(&self.header))?;
        writer.write_all(&self.palette)?;

        // for (i, item) in self.mips.iter().enumerate() {
        //     if let Some(mip) = item {
        //         println!("-mips[{i}] = {}", mip.len());
        //     }
        // }
        // let len: usize = self.mips.iter().map(|v| v.len()).sum();
        // let mut mpis_data: Vec<u8> = Vec::with_capacity(len);
        // unsafe {
        //     mpis_data.set_len(len);
        //     let mut offset = 0;
        //     for block in &self.mips {
        //         std::ptr::copy_nonoverlapping(
        //             block.as_ptr(),
        //             mpis_data.as_mut_ptr().add(offset),
        //             block.len(),
        //         );
        //         offset += block.len();
        //     }
        // }

        let mpis_data = self.mips
            .into_iter()
            .filter_map(|mut opt| opt.take())
            .flatten()
            .collect::<Vec<u8>>();

        writer.write_all(&mpis_data)?;
        writer.flush()?;

        Ok(())
    }
}

struct BitArray {
    buffer: Vec<u8>,
    length: usize, // number of bits
}

impl BitArray {
    fn with_length(length: usize) -> Self {
        let byte_len = (length + 7) / 8;
        Self {
            buffer: vec![0; byte_len],
            length,
        }
    }

    fn set(&mut self, index: usize, bit: u8) -> bool {
        if index >= self.length || (bit != 0 && bit != 1) {
            return false;
        }
        let byte = &mut self.buffer[index / 8];
        let mask = 1 << (index % 8);
        if bit == 1 {*byte |= mask;} else {*byte &= !mask;}
        true
    }
}

pub fn load_from_blp(
    image: &mut MemImage,
    filename: &Path,
) -> Result<FormatID, AppError> {
    let mut file = File::open(filename)?;

    if get_verbose() {
        println!("{}", filename.display());
    }
    
    // Ensure blp_type is assigned a valid FormatID before proceeding
    let mut file_buf = Vec::new();
    let bytes = file.read_to_end(&mut file_buf)?;

    if bytes < size_of::<BlpHeader>() {
        return Err(AppError::NotBlp);
    }

    let header = bytemuck::from_bytes::<BlpHeader>(&file_buf[..size_of::<BlpHeader>()]);

    match &header.id {
        b"BLP2" => {
            if header.version != 1 {
                return Err(AppError::VersionError);
            }
        }
        b"PTCH" => {
            return Err(AppError::Patch);
        }
        _ => return Err(AppError::Error("Unsupported format")),
    }

    if get_verbose() {
        println!("\t{}x{}", header.x_resolution, header.y_resolution);
        let encoding_str = if BlpEncoding::from(header.encoding) != BlpEncoding::Invalid {
            ENCODING_STRINGS[header.encoding as usize]
        } else {
            "Unrecognized"
        };
        println!("\tencoding = {} ({})", header.encoding, encoding_str);
        println!("\talphaBitDepth = {}", header.alpha_bit_depth);
        println!("\talphaEncoding = {}", header.alpha_encoding);

        if header.has_mips == 1 {
            let mip_count = header.mip_offsets
                .iter()
                .filter(|&offset| *offset != 0)
                .count();
            println!("\t{} mips", mip_count);
        } else {
            println!("\tno mips");
        }
    }

    let blp_type = match BlpEncoding::from(header.encoding) {
        BlpEncoding::Palettized => match load_palettized_data(image, &file_buf, header) {
            Ok(blp_type) => blp_type,
            Err(err) => return Err(AppError::Error(err)),
        },
        BlpEncoding::Compressed => match load_compressed_data(image, &file_buf, header) {
            Ok(blp_type) => blp_type,
            Err(err) => return Err(AppError::Error(err)),
        },
        BlpEncoding::Bgra => match load_bgra_data(image, &file_buf, header) {
            Ok(blp_type) => blp_type,
            Err(err) => return Err(AppError::Error(err)),
        },
        _ => return Err(AppError::Error("Unrecognized Encoding!")),
    };

    if get_verbose() {
        println!("\tFormat = {} ({}).", blp_type.name(), blp_type.description());
    }

    Ok(blp_type)
}

fn load_palettized_data(
    image: &mut MemImage,
    file_buf: &[u8],
    header: &BlpHeader,
) -> Result<FormatID, &'static str> {
    image.is_palettized = true;
    image.has_alpha = true;
    let mut bpp = 2;
    let blp_type = match header.alpha_bit_depth {
        0 => {
            bpp = 1;
            image.has_alpha = false;
            BlpPalA0
        }
        1 => BlpPalA1,
        4 => BlpPalA4,
        8 => BlpPalA8,
        _ => return Err("'alphaBitDepth' field an unrecognized value."),
    };

    let palette_offset = size_of::<BlpHeader>();

    image.m_palette.get_or_insert_with(|| {
        file_buf[palette_offset..palette_offset + 256 * 4]
        .chunks_exact(4)
        .map(|px| [px[2], px[1], px[0]])
        .collect()
    });

    image.m_width = header.x_resolution;
    image.m_height = header.y_resolution;
    let pixel_count = (image.m_width * image.m_height) as usize;

    let offset = palette_offset + 1024;
    let image_data = &file_buf[offset..];
    image.m_buffer.reserve_exact(pixel_count * bpp);

    match blp_type {
        BlpPalA0 | BlpPalA8 => {
            image.m_buffer.extend(&image_data[..pixel_count * bpp]);
        }
        BlpPalA1 => {
            let (data, alpha) = image_data.split_at(pixel_count);
            image.m_buffer.extend(data);
            for ii in 0..pixel_count {
                let bit = (alpha[ii / 8] >> (ii % 8)) & 0x1;
                image.m_buffer.push(if bit != 0 { 0xFF } else { 0 });
            }
        }
        BlpPalA4 => {
            let (data, alpha) = image_data.split_at(pixel_count);
            image.m_buffer.extend(data);
            for ii in 0..pixel_count {
                let byte = alpha[ii / 2];
                let shift = (ii % 2) * 4;
                let alpha4 = (byte >> shift) & 0xF;
                let alpha = ((alpha4 as f32 / 15.0) * 255.0).round() as u8;
                image.m_buffer.push(alpha);
            }
        }
        _ => unreachable!(),
    }

    Ok(blp_type)
}

fn load_compressed_data(
    image: &mut MemImage,
    file_buf: &[u8],
    header: &BlpHeader,
) -> Result<FormatID, &'static str> {
    image.has_alpha = true;
    let mut squish_flags = squish::K_DXT1;
    let mut bpp = 4;

    let blp_type = match header.alpha_bit_depth {
        0 => {
            bpp = 3;
            image.has_alpha = false;
            BlpDxt1A0
        }
        1 => BlpDxt1A1,
        4 => match header.alpha_encoding {
            1 => {
                squish_flags = squish::K_DXT3;
                BlpDxt3
            }
            _ => return Err("'alphaEncoding' field an unrecognized value."),
        },
        8 => match header.alpha_encoding {
            1 => {
                squish_flags = squish::K_DXT3;
                BlpDxt3
            }
            7 => {
                squish_flags = squish::K_DXT5;
                BlpDxt5
            }
            _ => return Err("'alphaEncoding' field an unrecognized value."),
        },
        72 => {
            // Special case (mostly doodads).
            println!("WARNING: alphaBitDepth = 72, treating as BlpDxt5.");
            squish_flags = squish::K_DXT5;
            BlpDxt5
        }
        _ => return Err("'alphaBitDepth' field an unrecognized value."),
    };

    // Read source and allocate dest
    image.m_width = header.x_resolution;
    image.m_height = header.y_resolution;
    let pixel_count = (image.m_width * image.m_height) as usize;
    let offset = size_of::<BlpHeader>() + 1024;
    let source = &file_buf[offset..];
    let mut dest = vec![0; pixel_count * 4];

    // Decompress using squish (or your custom DXT decoder)
    squish::decompress_image(
        &mut dest,
        image.m_width.try_into().unwrap(),
        image.m_height.try_into().unwrap(),
        source,
        squish_flags,
    );

    image.m_buffer.reserve_exact(pixel_count * bpp);

    if blp_type == BlpDxt1A0 {
        for i in 0..pixel_count {
            image.m_buffer.extend(&dest[i * 4..i * 4 + 3]);
            // No alpha in BlpDxt1A0, so we don't push alpha values
        }
    } else {
        image.m_buffer.extend(dest);
    }

    Ok(blp_type)
}

fn load_bgra_data(
    image: &mut MemImage,
    file_buf: &[u8],
    header: &BlpHeader,
) -> Result<FormatID, &'static str> {
    let blp_type = BlpBgra;
    image.has_alpha = true;

    if header.alpha_bit_depth != 8 {
        println!("alphaBitDepth is {}, expected 8. Will treat data as though it was 8.", header.alpha_bit_depth);
    }

    image.m_width = header.x_resolution;
    image.m_height = header.y_resolution;
    let pixel_count = (image.m_width * image.m_height) as usize;

    if pixel_count * 4 != header.mip_sizes[0] as usize {
        return Err("mip0 size unexpected.");
    }

    let offset = size_of::<BlpHeader>() + 1024;
    let source = &file_buf[offset..];
    image.m_buffer.resize(pixel_count * 4, 0);

    for i in 0..pixel_count {
        image.m_buffer[i * 4 + 0] = source[i * 4 + 2]; // B
        image.m_buffer[i * 4 + 1] = source[i * 4 + 1]; // G
        image.m_buffer[i * 4 + 2] = source[i * 4 + 0]; // R
        image.m_buffer[i * 4 + 3] = source[i * 4 + 3]; // A
    }

    Ok(blp_type)
}

pub fn save_to_blp<P: AsRef<Path>>(
    mut image: MemImage,
    filename: P,
    format: FormatID,
) -> Result<(), AppError> {
    // Check for conversion needs

    if BlpPalA0 <= format && format <= BlpPalA8 {
        if !image.is_palettized {
            image.palettize()?;
        }
    } else {
        if image.is_palettized {
            image.depalettize()?;
        }
    }

    if BlpPalA0 == format || format == BlpDxt1A0 {
        if image.has_alpha {
            image.remove_alpha()?;
        }
    } else {
        if !image.has_alpha {
            image.add_alpha()?;
        }
    }

    let mut blp_file = BlpFile::new();
    blp_file.header.x_resolution = image.m_width;
    blp_file.header.y_resolution = image.m_height;
    blp_file.header.has_mips = if get_no_mips() { 0 } else { 1 };
    if let Some(palette) = image.m_palette.as_ref() {
        blp_file.set_palette(palette);
    }

    match format {
        BlpPalA0 => {
            blp_file.header.alpha_bit_depth = 0; // No alpha
            blp_file.header.alpha_encoding = 8; // 8 value taken from example
            blp_file.header.encoding = BlpEncoding::Palettized as u8;
        }
        BlpPalA1 => {
            blp_file.header.alpha_bit_depth = 1; // 1-bit alpha
            blp_file.header.alpha_encoding = 1; // 1 value taken from example
            blp_file.header.encoding = BlpEncoding::Palettized as u8;
        }
        BlpPalA4 => {
            blp_file.header.alpha_bit_depth = 4; // 4-bit alpha
            blp_file.header.alpha_encoding = 8; // 8 value taken from example
            blp_file.header.encoding = BlpEncoding::Palettized as u8;
        }
        BlpPalA8 => {
            blp_file.header.alpha_bit_depth = 8; // Regular alpha
            blp_file.header.alpha_encoding = 8; // 8 value taken from example
            blp_file.header.encoding = BlpEncoding::Palettized as u8;
        }
        BlpDxt1A0 => {
            blp_file.header.alpha_bit_depth = 0; // No alpha
            blp_file.header.alpha_encoding = 0; // 0 = DXT1
            blp_file.header.encoding = BlpEncoding::Compressed as u8;
        }
        BlpDxt1A1 => {
            blp_file.header.alpha_bit_depth = 1; // No alpha
            blp_file.header.alpha_encoding = 0; // 0 = DXT1 
            blp_file.header.encoding = BlpEncoding::Compressed as u8;
        }
        BlpDxt3 => {
            blp_file.header.alpha_bit_depth = 8; // 
            blp_file.header.alpha_encoding = 1; // 1 = DXT3
            blp_file.header.encoding = BlpEncoding::Compressed as u8;
        }
        BlpDxt5 => {
            blp_file.header.alpha_bit_depth = 8; // 
            blp_file.header.alpha_encoding = 7; // 7 = DXT5
            blp_file.header.encoding = BlpEncoding::Compressed as u8;
        }
        BlpBgra => {
            blp_file.header.alpha_bit_depth = 8;
            blp_file.header.alpha_encoding = 8; // Totally guessing here.  I don't think it has any meaning.
            blp_file.header.encoding = BlpEncoding::Bgra as u8;
        }
        _ => unreachable!(),
    }

    let mip_count = (image.m_height).max(image.m_width).ilog2() + 1;
    let mips = image.build_mipmaps(mip_count);
    // for (i, mip) in mips.iter().enumerate() {
    //     println!("- mip[{}] = {}x{}", i, mip.m_width, mip.m_height);
    // }

    let mut offset = size_of::<BlpHeader>() + 1024;
    for i_level in 0..mip_count as usize {
        let mip_width = mips[i_level].m_width as usize;
        let mip_height = mips[i_level].m_height as usize;
        let mip_pixel_count = mip_width * mip_height;
        let src_mip_buffer = &mips[i_level].m_buffer;

        let converted_bytes = match format {
            BlpPalA0 => mip_pixel_count,
            BlpPalA1 => mip_pixel_count + (mip_pixel_count + 7) / 8,
            BlpPalA4 => mip_pixel_count + mip_pixel_count / 2,
            BlpPalA8 => mip_pixel_count * 2,
            BlpDxt1A0 => ((mip_width + 3) / 4) * ((mip_height + 3) / 4) * 8,
            BlpDxt1A1 => ((mip_width + 3) / 4) * ((mip_height + 3) / 4) * 8,
            BlpDxt3 => ((mip_width + 3) / 4) * ((mip_height + 3) / 4) * 16,
            BlpDxt5 => ((mip_width + 3) / 4) * ((mip_height + 3) / 4) * 16,
            BlpBgra => mip_pixel_count * 4,
            _ => unreachable!(),
        };

        blp_file.header.mip_sizes[i_level] = converted_bytes.try_into().unwrap();
        blp_file.header.mip_offsets[i_level] = offset.try_into().unwrap();
        offset += converted_bytes;
        blp_file.mips.push(Some(Vec::with_capacity(converted_bytes)));
        let converted_buffer = blp_file.mips.last_mut().unwrap().as_mut().unwrap();

        match format {
            BlpPalA0 => {   
                converted_buffer.extend(src_mip_buffer);
            }
            BlpPalA1 => {
                let (pixel_data, alpha_data)= src_mip_buffer.split_at(mip_pixel_count);
                converted_buffer.extend(pixel_data);

                let mut alpha_bits = BitArray::with_length(mip_pixel_count);
                for (i, &alpha) in alpha_data.iter().enumerate() {
                    alpha_bits.set(i, if alpha >= get_alpha_threshold() {0x1} else {0x0});
                }
                converted_buffer.extend(alpha_bits.buffer);
            }
            BlpPalA4 => {
                let (pixel_data, alpha_data)= src_mip_buffer.split_at(mip_pixel_count);
                converted_buffer.extend(pixel_data);
                converted_buffer.resize(converted_bytes, 0);

                for i in (0..mip_pixel_count - 1).step_by(2) {
                    let a0 = ((alpha_data[i] as f32 * get_gamma_factor()) as u8) >> 4;
                    let a1 = ((alpha_data[i + 1] as f32 * get_gamma_factor()) as u8) >> 4;
                    converted_buffer[mip_pixel_count + i / 2] = a0 + (a1 << 4);
                }
            }
            BlpPalA8 => {
                let (pixel_data, alpha_data)= src_mip_buffer.split_at(mip_pixel_count);
                converted_buffer.extend(pixel_data);

                for i in 0..mip_pixel_count {
                    converted_buffer.push(((alpha_data[i] as f32) * get_gamma_factor()) as u8);
                }
            }
            BlpDxt1A0 | BlpDxt1A1 | BlpDxt3 | BlpDxt5 => {
                let squish_flag = match format {
                    BlpDxt1A0 => squish::K_DXT1,
                    BlpDxt1A1 => squish::K_DXT1,
                    BlpDxt3 => squish::K_DXT3,
                    BlpDxt5 => squish::K_DXT5,
                    _ => unreachable!(),
                };
                
                let mut src: Vec<u8> = Vec::with_capacity(mip_pixel_count * 4);
                if format == BlpDxt1A0 {
                    src.extend(
                        src_mip_buffer
                        .chunks_exact(3)
                        .flat_map(|px| [px[0], px[1], px[2], 255])
                    );
                } else {
                    src.extend(src_mip_buffer);
                }

                squish::compress_image(
                    &src,
                    mip_width.try_into().unwrap(),
                    mip_height.try_into().unwrap(),
                    converted_buffer,
                    squish_flag,
                );
            }
            BlpBgra => {
                converted_buffer.extend(
                    src_mip_buffer
                        .chunks_exact(4)
                        .flat_map(|c| [c[2], c[1], c[0], c[3]])
                );
            }
            _ => unreachable!()
        }
    }

    blp_file.save(filename)?;

    println!("...done!");
    Ok(())
}