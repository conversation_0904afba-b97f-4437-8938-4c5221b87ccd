// 新的主模块 - 使用重构后的配置系统
mod img;
mod cfg;
mod blp;
mod png;
mod squish;
mod config;

use std::{
    io::{self, Write},
    path::Path,
};

use crate::img::MemImage;
use crate::cfg::*;
use crate::config::{Config, ConfigError};

fn process_file_with_config(
    input_path: &str,
    destination_filename: Option<&Path>,
    config: &Config,
) -> Result<(), AppError> {
    let input_path = Path::new(input_path);
    let filename_stem = input_path.file_stem().and_then(|s| s.to_str())
        .ok_or_else(|| AppError::ProcessingFailed(input_path.to_path_buf()))?;

    let extension = input_path.extension().and_then(|s| s.to_str())
        .ok_or_else(|| AppError::ProcessingFailed(input_path.to_path_buf()))?
        .to_lowercase();

    let mut image = MemImage::default();
    let input_format;

    if extension == "blp" {
        input_format = image.load_from_blp(input_path)?;
    } else if extension == "png" {
        input_format = image.load_from_png(input_path)?;
    } else {
        return Err(AppError::Error("Input file is not PNG or BLP"));
    }

    if config.verbose {
        println!("{}", input_path.display());
        println!("\t{}x{}", image.m_width, image.m_height);
        println!("\tFormat = {} ({}).", input_format.name(), input_format.description());
    }

    if config.info_mode {
        return Ok(());
    }

    let mut target_format = config.target_format;
    if target_format == FormatID::Unspecified {
        target_format = config.get_format_mapping(input_format);
    }

    let target_path = if let Some(dest) = destination_filename {
        dest.to_path_buf()
    } else {
        let same_type = (input_format.is_blp() && target_format.is_blp())
            || (input_format.is_png() && target_format.is_png());

        let ext = if target_format.is_blp() { "blp" } else { "png" };
        let suffix = if same_type { "_" } else { "" };

        input_path.with_file_name(format!("{}{}.{ext}", filename_stem, suffix))
    };

    if !config.info_mode {
        println!(
            "Converting: {} ({}) -> {} ({})",
            input_path.display(),
            input_format.name(),
            target_path.display(),
            target_format.name(),
        );
    }

    image.save(&target_path, target_format)?;

    Ok(())
}

fn list_formats() {
    println!("****************");
    println!("* File Formats *");
    println!("****************");
    println!("Format Name\tDescription");
    println!("________________________________________________");
    for &(name, description) in FORMAT_INFO.iter().skip(1) {
        println!("{}  \t {}", name, description);
    }
    println!("\n**************************");
    println!("* Conversion Rules Table *");
    println!("**************************");
    println!("Source Format\t Target Format");
    println!("________________________________________________");
    {
        let rules = FORMAT_RULES.read().unwrap();
        for (source, target) in rules.iter().skip(1) {
            println!("{:<10}  ->  {}", source.name(), target.name());
        }
    }
    println!("\nThe Conversion Rules table shows the format given to the destination file");
    println!("when the source file has a given format. You can change a rule with the /U");
    println!("option, or you can force the destination file into a given format with /F.");
    println!("/U can be specified multiple times. Blp->Blp and Png->Png is OK.");
    println!("\nExamples:");
    println!("  blpconverter /FPngRgb myfile.blp");
    println!("  blpconverter /UBlpPalA0=PngRgb /UPngPal=PngRgb myfile.blp\n");
}

fn usage() {
    const INDENT: &str = "\n            ";
    
    println!("\nBLP2PNG: Converts BLP files to PNGs and vice versa.");
    println!("Version 1.0 (C) 2025 Andy Zih (<EMAIL>)");
    println!("This program is free software under the GNU General Public License.");
    println!("BLPCONVERTER [options] sourceFile [targetFile | sourceFile [...]]");
    println!("sourceFile The file to convert.");
    println!("targetFile Optionally, the name of the converted file. If omitted, target{}file is given the same name as sourceFile but with the opposite{}extension.", INDENT, INDENT);
    println!("/A(value)   Sets the Alpha threshold when converting from palettized, 8-bit{}BLPs to palettized PNGs. Value is a number between 0 and 255.{}Source alpha values below the threshold are fully transparent, above{}are fully opaque. Default is {}.", INDENT, INDENT, INDENT, 0x80);
    println!("/C          Create mip test image. Outputs an image which contains all of the{}generated mip levels.", INDENT);
    println!("/E          Pause on Error. (Handy for drag-and-drop use.)");
    println!("/F(format)  Forces target Format. Overrides all other settings, including{}targetFile extension.", INDENT);
    println!("/H          Force WoW cHaracter texture format (palettized, no alpha) when{}making BLPs.", INDENT);
    println!("/I          Info mode. Only outputs file information. This option{}automatically sets the /V and /M options.", INDENT);
    println!("/L          Lists formats and conversion rules.");
    println!("/M          Multi-file mode. In this mode, multiple files can be input after{}options. It is not possible to specify custom output names for them{}in this mode.", INDENT, INDENT);
    println!("/N          No mips. Disables creation of mip levels when saving BLPs.");
    println!("/P          Pause upon completion. (Handy for drag-and-drop use.)");
    println!("/R          Force WoW clothing texture formats. All created BLPs are palettized{}and all PNGs are RGB/RGBA.", INDENT);
    println!("/U(format)=(format){}Change conversion rUle. See /L.", INDENT);
    println!("/V          Verbose mode. Outputs additional information.");
}

fn run_with_config(config: &Config) -> Result<(), AppError> {
    if config.multi_file_mode {
        for pattern in &config.source_files {
            for entry in glob::glob(&pattern.to_string_lossy())? {
                let path = entry?;
                if path.is_file() {
                    process_file_with_config(path.to_str().unwrap(), None, config)?;
                }
            }
        }
    } else {
        let source = &config.source_files[0];
        let dest = config.dest_file.as_ref().map(|s| s.as_path());
        process_file_with_config(&source.to_string_lossy(), dest, config)?;
    }
    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = match Config::from_args() {
        Ok(config) => config,
        Err(ConfigError::HelpRequested) => {
            usage();
            return Ok(());
        }
        Err(ConfigError::NoArguments) => {
            usage();
            return Ok(());
        }
        Err(e) => {
            eprintln!("Configuration error: {}", e);
            return Err(Box::new(e));
        }
    };

    // 应用配置到全局状态（临时兼容性措施）
    apply_config_to_globals(&config);

    let result = run_with_config(&config);

    let should_pause = config.pause_at_end
        || (config.pause_on_error && result.is_err());

    if should_pause {
        println!("Press Enter to continue...");
        let _ = io::stdout().flush();
        let mut buffer = String::new();
        let _ = io::stdin().read_line(&mut buffer)?;
    }

    result.map_err(|e| Box::new(e) as Box<dyn std::error::Error>)
}

// 临时函数：将新配置应用到旧的全局状态
// 这是为了保持向后兼容性，最终会被移除
fn apply_config_to_globals(config: &Config) {
    set_alpha_threshold(config.alpha_threshold);
    set_verbose(config.verbose);
    set_no_mips(config.no_mips);
    set_target_format(config.target_format);
    
    // 应用格式映射规则
    for &(from, to) in &config.format_rules {
        set_mapping(from, to);
    }
}
