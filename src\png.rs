use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, BufReader};
use crate::img::MemImage;
use crate::cfg::{FormatID::*, *};
use png::{Decoder, BitDepth, ColorType, Encoder, Compression, FilterType};
//use std::collections::HashMap;
use std::path::Path;

pub fn load_from_png(image: &mut MemImage, filename: &Path) -> Result<FormatID, AppError> {

    let file = File::open(filename)?;

    if get_verbose() {
        println!("{}", filename.display());
    }

    let decoder = Decoder::new(BufReader::new(file));

    let mut reader = decoder.read_info()?;

    let mut buf = vec![0u8; reader.output_buffer_size()];
    
    let pixel = reader.next_frame(&mut buf)?;

    let bytes = &buf[..pixel.buffer_size()];

    let info = reader.info();
    image.m_width = info.width;
    image.m_height = info.height;
    let pixel_count = (image.m_width * image.m_height) as usize;

    image.m_buffer.reserve_exact(pixel_count * 4);
    image.m_buffer.extend(bytes);

    if get_verbose() {
        println!("\t{}x{}", image.m_width, image.m_height);
    }

    let pixel_count = (image.m_width * image.m_height) as usize;
    let png_type;
    match info.color_type {
        ColorType::Rgba => {
            image.has_alpha = true;
            png_type = PngRgba;
        }
        ColorType::Rgb => {
            png_type = PngRgb;
        }
        ColorType::Indexed => {
            image.is_palettized = true;

            if let Some(pal) = info.palette.as_ref() {
                image.m_palette.get_or_insert_with(|| {pal
                    .chunks_exact(3)
                    .map(|px| [px[0], px[1], px[2]])
                    .collect()
                });

                if get_verbose() {
                    println!("\t{} palette entries", pal.len() / 3);
                }
            };

            match info.trns.as_ref() {
                Some(trns) => {
                    png_type = PngPalMask;

                    if get_verbose() {
                        println!("\t{} transparency values", trns.len());
                    }

                    image.has_alpha = true;
                    let mut tmp_trns = Vec::with_capacity(pixel_count);
                    for i in bytes {
                        let a = trns[*i as usize];
                        tmp_trns.push(a);
                    }
                    image.m_buffer.extend(tmp_trns);
                } 
                _ => png_type = PngPal,
            }
        }
        ColorType::Grayscale => {
            png_type = PngRgb;
            image.m_buffer.clear();
            for i in 0..pixel_count {
                let px = bytes[i];
                image.m_buffer.extend([px, px, px]);
            }
        }
        ColorType::GrayscaleAlpha => {
            png_type = PngRgba;
            image.m_buffer.clear();
            image.has_alpha = true;
            for px in bytes.chunks_exact(2) {
                image.m_buffer.extend([px[0], px[0], px[0], px[1]]);
            }
        }
    }

    if get_verbose() {
        println!("\tFormat = {} ({}).", png_type.name(), png_type.description());
    }

    Ok(png_type)
}

pub fn save_to_png(mut img: MemImage, filename: &Path, format: FormatID) -> Result<(), AppError> {
    
    if PngPal == format || format == PngPalMask {
        if !img.is_palettized {
            img.palettize()?;
        }
    } else {
        if img.is_palettized {
            img.depalettize()?;
        }
    }

    if format == PngRgb || PngPal == format {
        if img.has_alpha {
            img.remove_alpha()?;
        }
    } else {
        if !img.has_alpha {
            img.add_alpha()?;
        }
    }

    // Initialize the PNG writer
    let pixel_count = (img.m_width * img.m_height) as usize;
    let mut converted_buffer: Vec<u8> = Vec::with_capacity(pixel_count * 4);
    
    let file = File::create(filename)?;
    let ref mut writer = BufWriter::new(file);
    let mut encoder = Encoder::new(writer, img.m_width, img.m_height);
    encoder.set_compression(Compression::Best);
    encoder.set_filter(FilterType::NoFilter);

    match format {
        PngPal | PngPalMask => {
            encoder.set_color(ColorType::Indexed);
            encoder.set_depth(BitDepth::Eight);

            let palette = img.m_palette.unwrap()
                .iter()
                .flat_map(|c| [c[0], c[1], c[2]])
                .collect::<Vec<u8>>();
            encoder.set_palette(palette);
            let alpha_data = img.m_buffer.split_off(pixel_count);
            if format == PngPalMask {
                let trns = construct_trns_alpha_palette(&img.m_buffer, &alpha_data);
                encoder.set_trns(trns);
            };
            converted_buffer.extend(img.m_buffer);
        }
        PngRgb | PngRgba => {
            if format == PngRgba {
                encoder.set_color(ColorType::Rgba);
                encoder.set_depth(BitDepth::Eight);
            } else {
                encoder.set_color(ColorType::Rgb);               
            }
            converted_buffer.extend(img.m_buffer);
        }
        _ => unreachable!(),
    }

    let mut writer = encoder.write_header()?;
    writer.write_image_data(&converted_buffer)?;

    println!("...done!");
    Ok(())
}

pub fn construct_trns_alpha_palette(
    rgb_palette_indices: &[u8],
    pure_alpha_data: &[u8],
    //default_alpha_for_unused_indices: u8,
) -> Vec<u8> {
    use std::collections::HashMap;

    assert_eq!(
        rgb_palette_indices.len(),
        pure_alpha_data.len(),
        "Input arrays length mismatch!"
    );

    // 对每个 index 统计 (sum_alpha, count)
    let mut alpha_stat: HashMap<u8, (u32, u32)> = HashMap::new();

    for (&idx, &alpha) in rgb_palette_indices.iter().zip(pure_alpha_data.iter()) {
        let entry = alpha_stat.entry(idx).or_insert((0, 0));
        entry.0 += alpha as u32;
        entry.1 += 1;
    }

    let mut trns = vec![255; 256];

    for idx in 0u8..=255 {
        if let Some(&(sum, count)) = alpha_stat.get(&idx) {
            let mut avg = (sum / count) as u8;

            // 可选修正：避免低 alpha 区域“脏”，加个 bias 提亮低透明
            if avg > 0 && avg < 16 {
                avg = 16;  // 把太暗的 alpha 拉到至少16，避免脏点
            }

            trns[idx as usize] = avg;
        }
    }

    trns
}

/* 
/// Constructs a 256-entry Alpha (tRNS-like) palette based on existing RGB palette indices and pure alpha data.
///
/// This function attempts to determine the "dominant" alpha value for each of the
/// 256 RGB palette entries. It does this by counting the occurrences of each alpha
/// value for a given palette index. The most frequent alpha value for each index
/// is chosen as its corresponding alpha in the tRNS palette.
///
/// If a palette index has no corresponding pixels in the input data, its alpha
/// value in the output tRNS palette will be set to `default_alpha_for_unused_indices`.
///
/// # Arguments
/// * `rgb_palette_indices` - A slice of `u8` representing the palette index for each pixel.
/// * `pure_alpha_data` - A slice of `u8` representing the pure 8-bit alpha value for each pixel.
/// * `default_alpha_for_unused_indices` - The alpha value to use for palette indices
///                                        that do not appear in `rgb_palette_indices`.
///
/// # Returns
/// `Vec<u8>`: A 256-entry `Vec<u8>` representing the tRNS-like alpha palette.
///
/// # Panics
/// Panics if `rgb_palette_indices` and `pure_alpha_data` do not have the same length.
pub fn construct_trns_alpha_palette(
    rgb_palette_indices: &[u8],
    pure_alpha_data: Vec<u8>,
    //default_alpha_for_unused_indices: u8,
) -> Vec<u8> {
    assert_eq!(
        rgb_palette_indices.len(),
        pure_alpha_data.len(),
        "Input arrays must have the same length."
    );

    // This HashMap will store for each palette index (key), another HashMap
    // that counts the occurrences of each alpha value (value).
    // Outer HashMap: palette_index -> (Inner HashMap: alpha_value -> count)
    let mut alpha_counts_per_palette_index: HashMap<u8, HashMap<u8, u32>> = HashMap::new();

    // 1. Collect alpha counts for each palette index
    for i in 0..rgb_palette_indices.len() {
        let palette_idx = rgb_palette_indices[i];
        let alpha_val = pure_alpha_data[i];

        // Get the inner HashMap for this palette_idx, or insert a new one if it doesn't exist
        let entry = alpha_counts_per_palette_index
            .entry(palette_idx)
            .or_insert_with(HashMap::new); // Or use `HashMap::new()` in older Rust

        // Increment the count for this alpha_val
        *entry.entry(alpha_val).or_insert(0) += 1;
    }

    // 2. Determine the dominant alpha for each palette index
    let mut trns_alpha_palette = vec![255; 256];

    for palette_idx in 0..=default_alpha_for_unused_indices {
        if let Some(alpha_counts) = alpha_counts_per_palette_index.get(&palette_idx) {
            // Find the alpha value with the maximum count for this palette_idx
            let mut dominant_alpha = default_alpha_for_unused_indices;
            let mut max_count = 0;

            for (&alpha_val, &count) in alpha_counts {
                if count > max_count {
                    max_count = count;
                    dominant_alpha = alpha_val;
                }
                // Optional: If counts are equal, you might have a tie-breaking rule,
                // e.g., choose the smaller alpha value, or prioritize a certain transparency.
                // For now, it will simply pick the first one encountered with max_count.
            }
            trns_alpha_palette[palette_idx as usize] = dominant_alpha;
        }
        // If `alpha_counts_per_palette_index.get(&palette_idx)` returns `None`,
        // it means this `palette_idx` was not present in the input data.
        // In this case, it retains the `default_alpha_for_unused_indices` value.
    }

    trns_alpha_palette
}
    */