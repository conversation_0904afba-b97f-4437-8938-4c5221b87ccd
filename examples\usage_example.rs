// 使用新架构的示例程序
use std::path::Path;

// 这个示例展示了如何使用重构后的架构
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 BLP2PNG 新架构使用示例");
    
    // 示例1: 基本配置创建
    example_basic_config()?;
    
    // 示例2: 处理管道使用
    example_pipeline_usage()?;
    
    // 示例3: 格式注册表使用
    example_format_registry()?;
    
    println!("✅ 所有示例运行完成！");
    Ok(())
}

fn example_basic_config() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📋 示例1: 基本配置创建");
    
    // 创建默认配置
    let mut config = blp2png::config::Config::default();
    
    // 修改配置
    config.verbose = true;
    config.alpha_threshold = 128;
    config.target_format = blp2png::cfg::FormatID::PngRgba;
    
    println!("  ✓ 配置创建成功");
    println!("    - 详细模式: {}", config.verbose);
    println!("    - Alpha阈值: {}", config.alpha_threshold);
    println!("    - 目标格式: {:?}", config.target_format);
    
    Ok(())
}

fn example_pipeline_usage() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 示例2: 处理管道使用");
    
    // 创建配置
    let config = blp2png::config::Config {
        verbose: true,
        multi_file_mode: false,
        target_format: blp2png::cfg::FormatID::PngRgba,
        ..Default::default()
    };
    
    // 创建处理管道
    let pipeline = blp2png::pipeline::ProcessingPipeline::new(config);
    
    println!("  ✓ 处理管道创建成功");
    
    // 注意：实际文件处理需要真实的文件
    // let result = pipeline.process_file(Path::new("test.blp"), None)?;
    
    Ok(())
}

fn example_format_registry() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 示例3: 格式注册表使用");
    
    // 创建格式注册表
    let registry = blp2png::image::FormatRegistry::new();
    
    // 获取支持的扩展名
    let extensions = registry.supported_extensions();
    println!("  ✓ 支持的格式: {:?}", extensions);
    
    // 检查特定格式支持
    if let Some(blp_format) = registry.get_format("blp") {
        println!("  ✓ BLP格式处理器: {}", blp_format.format_name());
    }
    
    if let Some(png_format) = registry.get_format("png") {
        println!("  ✓ PNG格式处理器: {}", png_format.format_name());
    }
    
    Ok(())
}

// 高级示例：自定义格式处理器
#[allow(dead_code)]
struct CustomFormat;

impl blp2png::image::ImageFormat for CustomFormat {
    fn load(&self, _path: &Path, _config: &blp2png::config::Config) -> Result<blp2png::image::Image, blp2png::cfg::AppError> {
        // 自定义加载逻辑
        todo!("实现自定义格式加载")
    }
    
    fn save(&self, _image: &blp2png::image::Image, _path: &Path, _config: &blp2png::config::Config) -> Result<(), blp2png::cfg::AppError> {
        // 自定义保存逻辑
        todo!("实现自定义格式保存")
    }
    
    fn supports_extension(&self, ext: &str) -> bool {
        ext.eq_ignore_ascii_case("custom")
    }
    
    fn format_name(&self) -> &'static str {
        "Custom Format"
    }
    
    fn supported_format_ids(&self) -> &[blp2png::cfg::FormatID] {
        &[]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_examples() {
        // 测试示例函数不会崩溃
        assert!(example_basic_config().is_ok());
        assert!(example_pipeline_usage().is_ok());
        assert!(example_format_registry().is_ok());
    }
    
    #[test]
    fn test_custom_format() {
        let custom = CustomFormat;
        assert_eq!(custom.format_name(), "Custom Format");
        assert!(custom.supports_extension("custom"));
        assert!(!custom.supports_extension("blp"));
    }
}
