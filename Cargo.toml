[package]
name = "BLP2PNG"
version = "0.1.0"
edition = "2024"

[build-dependencies]
cc = "1.2.24"

[dependencies]
bytemuck = {version = "1.23.1", features = ["derive"]}
glob = "0.3.2"
imagequant = "4.3.4"
memmap2 = "0.9.5"
once_cell = "1.21.3"
png = "0.17.16"
thiserror = "1.0"
anyhow = "1.0"

[profile.release]
opt-level = 3
lto = true
panic = "abort"
codegen-units = 1
debug = false

[[bin]]
name = "benchmark"
path = "benchmark.rs"