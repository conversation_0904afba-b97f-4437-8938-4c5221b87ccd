// 新的图像处理抽象层
use std::path::Path;
use crate::config::Config;
use crate::cfg::{AppError, FormatID};

pub mod formats;
pub mod processing;

/// 通用图像数据结构
#[derive(Debug, <PERSON>lone)]
pub struct Image {
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub format: PixelFormat,
    pub palette: Option<Vec<[u8; 3]>>,
}

/// 像素格式定义
#[derive(Debug, Clone, PartialEq)]
pub enum PixelFormat {
    /// RGB格式，每像素3字节
    Rgb,
    /// RGBA格式，每像素4字节
    Rgba,
    /// 索引格式
    Indexed { 
        has_alpha: bool,
        bits_per_pixel: u8,
    },
    /// 压缩格式
    Compressed {
        format: CompressionFormat,
        has_alpha: bool,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum CompressionFormat {
    Dxt1,
    Dxt3,
    Dxt5,
}

impl PixelFormat {
    /// 获取每像素字节数
    pub fn bytes_per_pixel(&self) -> usize {
        match self {
            PixelFormat::Rgb => 3,
            PixelFormat::Rgba => 4,
            PixelFormat::Indexed { has_alpha, .. } => {
                if *has_alpha { 2 } else { 1 }
            }
            PixelFormat::Compressed { .. } => 4, // 解压后的格式
        }
    }
    
    /// 是否有alpha通道
    pub fn has_alpha(&self) -> bool {
        match self {
            PixelFormat::Rgb => false,
            PixelFormat::Rgba => true,
            PixelFormat::Indexed { has_alpha, .. } => *has_alpha,
            PixelFormat::Compressed { has_alpha, .. } => *has_alpha,
        }
    }
    
    /// 是否是调色板格式
    pub fn is_indexed(&self) -> bool {
        matches!(self, PixelFormat::Indexed { .. })
    }
}

/// 图像格式处理trait
pub trait ImageFormat: Send + Sync {
    /// 从文件加载图像
    fn load(&self, path: &Path, config: &Config) -> Result<Image, AppError>;
    
    /// 保存图像到文件
    fn save(&self, image: &Image, path: &Path, config: &Config) -> Result<(), AppError>;
    
    /// 检查是否支持指定扩展名
    fn supports_extension(&self, ext: &str) -> bool;
    
    /// 获取格式名称
    fn format_name(&self) -> &'static str;
    
    /// 获取支持的FormatID列表
    fn supported_format_ids(&self) -> &[FormatID];
}

impl Image {
    /// 创建新的图像
    pub fn new(width: u32, height: u32, format: PixelFormat) -> Self {
        let pixel_count = (width * height) as usize;
        let data_size = pixel_count * format.bytes_per_pixel();
        
        Self {
            data: vec![0; data_size],
            width,
            height,
            format,
            palette: None,
        }
    }
    
    /// 获取像素数量
    pub fn pixel_count(&self) -> usize {
        (self.width * self.height) as usize
    }
    
    /// 转换为RGB格式
    pub fn to_rgb(&mut self) -> Result<(), AppError> {
        if matches!(self.format, PixelFormat::Rgb) {
            return Ok(());
        }
        
        match &self.format {
            PixelFormat::Rgba => {
                // 移除alpha通道
                let mut new_data = Vec::with_capacity(self.pixel_count() * 3);
                for chunk in self.data.chunks_exact(4) {
                    new_data.extend_from_slice(&chunk[0..3]);
                }
                self.data = new_data;
                self.format = PixelFormat::Rgb;
            }
            PixelFormat::Indexed { .. } => {
                self.depalettize()?;
                if self.format.has_alpha() {
                    self.to_rgb()?; // 递归调用处理RGBA->RGB
                }
            }
            _ => return Err(AppError::Error("Unsupported format conversion")),
        }
        
        Ok(())
    }
    
    /// 转换为RGBA格式
    pub fn to_rgba(&mut self) -> Result<(), AppError> {
        if matches!(self.format, PixelFormat::Rgba) {
            return Ok(());
        }
        
        match &self.format {
            PixelFormat::Rgb => {
                // 添加alpha通道
                let mut new_data = Vec::with_capacity(self.pixel_count() * 4);
                for chunk in self.data.chunks_exact(3) {
                    new_data.extend_from_slice(chunk);
                    new_data.push(255); // 完全不透明
                }
                self.data = new_data;
                self.format = PixelFormat::Rgba;
            }
            PixelFormat::Indexed { .. } => {
                self.depalettize()?;
                if !self.format.has_alpha() {
                    self.to_rgba()?; // 递归调用处理RGB->RGBA
                }
            }
            _ => return Err(AppError::Error("Unsupported format conversion")),
        }
        
        Ok(())
    }
    
    /// 转换为索引格式
    pub fn to_indexed(&mut self, has_alpha: bool) -> Result<(), AppError> {
        if self.format.is_indexed() {
            return Ok(());
        }
        
        // 确保格式正确
        if has_alpha && !self.format.has_alpha() {
            self.to_rgba()?;
        } else if !has_alpha && self.format.has_alpha() {
            self.to_rgb()?;
        }
        
        self.palettize(has_alpha)?;
        Ok(())
    }
    
    /// 调色板化
    fn palettize(&mut self, has_alpha: bool) -> Result<(), AppError> {
        use imagequant::{Attributes as Attr, RGBA};
        
        let bpp = if has_alpha { 4 } else { 3 };
        let pixel_count = self.pixel_count();
        
        // 转换为RGBA格式用于量化
        let mut rgba = Vec::with_capacity(pixel_count);
        for chunk in self.data.chunks_exact(bpp) {
            rgba.push(RGBA {
                r: chunk[0],
                g: chunk[1],
                b: chunk[2],
                a: if has_alpha { chunk[3] } else { 255 },
            });
        }
        
        // 执行量化
        let mut attr = Attr::new();
        attr.set_max_colors(256)?;
        
        let mut img = attr.new_image(rgba, self.width as usize, self.height as usize, 0.0)?;
        let mut res = attr.quantize(&mut img)?;
        
        // 构建调色板
        let mut palette = Vec::with_capacity(256);
        for color in res.palette() {
            palette.push([color.r, color.g, color.b]);
        }
        palette.resize(256, [0; 3]);
        
        // 获取索引数据
        let (_, indices) = res.remapped(&mut img)?;
        
        // 重构数据
        if has_alpha {
            let mut new_data = Vec::with_capacity(pixel_count * 2);
            new_data.extend(indices);
            
            // 添加alpha数据
            for chunk in self.data.chunks_exact(bpp) {
                new_data.push(if has_alpha { chunk[3] } else { 255 });
            }
            
            self.data = new_data;
        } else {
            self.data = indices;
        }
        
        self.palette = Some(palette);
        self.format = PixelFormat::Indexed { 
            has_alpha, 
            bits_per_pixel: 8 
        };
        
        Ok(())
    }
    
    /// 去调色板化
    fn depalettize(&mut self) -> Result<(), AppError> {
        let PixelFormat::Indexed { has_alpha, .. } = &self.format else {
            return Ok(());
        };
        
        let has_alpha = *has_alpha;
        let pixel_count = self.pixel_count();
        let palette = self.palette.take()
            .ok_or(AppError::Error("Missing palette for indexed image"))?;
        
        let (indices, alpha_data) = if has_alpha {
            self.data.split_at(pixel_count)
        } else {
            (self.data.as_slice(), &[][..])
        };
        
        // 重构RGB(A)数据
        let mut new_data = Vec::with_capacity(pixel_count * if has_alpha { 4 } else { 3 });
        
        for (i, &idx) in indices.iter().enumerate() {
            let [r, g, b] = palette[idx as usize];
            new_data.extend_from_slice(&[r, g, b]);
            
            if has_alpha {
                new_data.push(alpha_data[i]);
            }
        }
        
        self.data = new_data;
        self.format = if has_alpha { 
            PixelFormat::Rgba 
        } else { 
            PixelFormat::Rgb 
        };
        
        Ok(())
    }
}
