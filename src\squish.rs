// src/squish.rs
#[allow(non_camel_case_types)]
pub type flags = i32;

pub const K_DXT1: flags = 1 << 0;
pub const K_DXT3: flags = 1 << 1;
pub const K_DXT5: flags = 1 << 2;

unsafe extern "C" {
    // fn rsq_GetStorageRequirements(width: i32, height: i32, flags: flags) -> i32;

    fn rsq_compress_image(
        rgba: *const u8,
        width: i32,
        height: i32,
        blocks: *mut u8,
        flags: flags,
    );

    fn rsq_decompress_image(
        rgba: *mut u8,
        width: i32,
        height: i32,
        blocks: *const u8,
        flags: flags,
    );
}


pub fn compress_image(rgba: &[u8], width: i32, height: i32, blocks: &mut Vec<u8>, flags: flags) {
    if blocks.capacity() != blocks.len() {
        blocks.resize(blocks.capacity(), 0);
    }
    unsafe {
        rsq_compress_image(
            rgba.as_ptr(),
            width,
            height,
            blocks.as_mut_ptr(),
            flags,
        );
    }
}

pub fn decompress_image(rgba: &mut [u8], width: i32, height: i32, blocks: &[u8], flags: flags) {
    // let size: usize = unsafe {rsq_GetStorageRequirements(width, height, flags)}.try_into().unwrap();
    // let blocks = &blocks[..size];
    unsafe {
        rsq_decompress_image(
            rgba.as_mut_ptr(),
            width,
            height,
            blocks.as_ptr(),
            flags,
        );
    }
}
