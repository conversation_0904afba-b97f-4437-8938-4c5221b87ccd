# 🏗️ BLP2PNG 架构优化建议

## 当前架构问题分析

### 1. **全局状态管理问题**
```rust
// 当前: 使用全局静态变量
static S_BY_ALPHA_THRESHOLD: Lazy<RwLock<u8>> = Lazy::new(|| RwLock::new(0x80));
static S_B_VERBOSE: Lazy<RwLock<bool>> = Lazy::new(|| RwLock::new(false));
```
**问题**: 
- 线程安全开销
- 测试困难
- 状态管理混乱

### 2. **紧耦合的模块设计**
- `MemImage` 直接调用 `blp::load_from_blp()` 和 `png::load_from_png()`
- 配置散布在各个模块中
- 错误处理不一致

### 3. **命令行解析过于复杂**
- 298行的main.rs包含太多逻辑
- 参数解析和业务逻辑混合

## 🚀 推荐的新架构

### 1. **配置管理重构**

```rust
// 新建 src/config.rs
#[derive(Debug, Clone)]
pub struct Config {
    pub alpha_threshold: u8,
    pub verbose: bool,
    pub no_mips: bool,
    pub gamma_factor: f32,
    pub target_format: FormatID,
    pub multi_file_mode: bool,
    pub pause_at_end: bool,
    pub pause_on_error: bool,
    // ... 其他配置
}

impl Default for Config {
    fn default() -> Self {
        Self {
            alpha_threshold: 0x80,
            verbose: false,
            no_mips: false,
            gamma_factor: 1.0,
            target_format: FormatID::Unspecified,
            multi_file_mode: false,
            pause_at_end: false,
            pause_on_error: false,
        }
    }
}

impl Config {
    pub fn from_args() -> Result<Self, AppError> {
        // 使用 clap 或 structopt 进行参数解析
    }
}
```

### 2. **图像处理抽象层**

```rust
// 新建 src/image/mod.rs
pub mod formats;
pub mod processing;
pub mod io;

pub trait ImageFormat {
    fn load(&self, path: &Path, config: &Config) -> Result<Image, AppError>;
    fn save(&self, image: &Image, path: &Path, config: &Config) -> Result<(), AppError>;
    fn supports_extension(&self, ext: &str) -> bool;
}

pub struct Image {
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub format: PixelFormat,
    pub palette: Option<Vec<[u8; 3]>>,
}

#[derive(Debug, Clone)]
pub enum PixelFormat {
    Rgb,
    Rgba,
    Indexed { has_alpha: bool },
    // ... 其他格式
}
```

### 3. **插件化格式支持**

```rust
// src/formats/blp.rs
pub struct BlpFormat;

impl ImageFormat for BlpFormat {
    fn load(&self, path: &Path, config: &Config) -> Result<Image, AppError> {
        // BLP加载逻辑
    }
    
    fn save(&self, image: &Image, path: &Path, config: &Config) -> Result<(), AppError> {
        // BLP保存逻辑
    }
    
    fn supports_extension(&self, ext: &str) -> bool {
        ext.eq_ignore_ascii_case("blp")
    }
}

// src/formats/png.rs
pub struct PngFormat;
impl ImageFormat for PngFormat { /* ... */ }
```

### 4. **处理管道架构**

```rust
// src/pipeline.rs
pub struct ProcessingPipeline {
    formats: HashMap<String, Box<dyn ImageFormat>>,
    config: Config,
}

impl ProcessingPipeline {
    pub fn new(config: Config) -> Self {
        let mut formats: HashMap<String, Box<dyn ImageFormat>> = HashMap::new();
        formats.insert("blp".to_string(), Box::new(BlpFormat));
        formats.insert("png".to_string(), Box::new(PngFormat));
        
        Self { formats, config }
    }
    
    pub fn process_file(&self, input: &Path, output: Option<&Path>) -> Result<(), AppError> {
        // 1. 检测输入格式
        // 2. 加载图像
        // 3. 应用转换
        // 4. 保存输出
    }
    
    pub fn process_batch(&self, files: &[PathBuf]) -> Result<Vec<ProcessResult>, AppError> {
        // 批处理逻辑
    }
}
```

## 🔧 具体优化步骤

### 第一阶段: 配置重构
1. 创建统一的Config结构体
2. 移除全局静态变量
3. 使用依赖注入传递配置

### 第二阶段: 模块解耦
1. 抽象ImageFormat trait
2. 重构MemImage为通用Image结构
3. 分离格式特定逻辑

### 第三阶段: 错误处理改进
1. 使用thiserror简化错误定义
2. 实现结构化错误报告
3. 添加错误恢复机制

### 第四阶段: 性能优化
1. 实现并行处理管道
2. 添加内存池管理
3. 优化热路径

## 📦 推荐的依赖更新

```toml
[dependencies]
# 命令行解析
clap = { version = "4.0", features = ["derive"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 并行处理
rayon = "1.7"

# 序列化(用于配置)
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"

# 日志
log = "0.4"
env_logger = "0.10"

# 异步I/O (可选)
tokio = { version = "1.0", features = ["full"], optional = true }
```

## 🎯 预期收益

### 可维护性
- 模块职责清晰
- 易于测试
- 便于扩展新格式

### 性能
- 减少全局锁竞争
- 更好的内存管理
- 并行处理能力

### 用户体验
- 更好的错误信息
- 进度报告
- 配置文件支持

## 🚧 迁移策略

1. **渐进式重构**: 保持向后兼容
2. **特性开关**: 新旧架构并存
3. **全面测试**: 确保功能一致性
4. **性能基准**: 验证优化效果

这种架构重构将使代码更加模块化、可测试和可扩展，同时为未来的功能添加奠定坚实基础。
