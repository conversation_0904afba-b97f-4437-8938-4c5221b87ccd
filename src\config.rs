// 统一的配置和类型定义模块
use std::path::PathBuf;
use std::fmt;

// ============================================================================
// 核心类型定义
// ============================================================================

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, PartialOrd)]
pub enum FormatID {
    Unspecified = 0,
    BlpPalA0,
    BlpPalA1,
    BlpPalA4,
    BlpPalA8,
    BlpDxt1A0,
    BlpDxt1A1,
    BlpDxt3,
    BlpDxt5,
    BlpBgra,
    PngPal,
    PngPalMask,
    PngRgb,
    PngRgba,
    FormatCount,
}

pub const FORMAT_INFO: &[(&'static str, &'static str)] = &[
    ("Unspecified", "[INVALID]"),
    ("BlpPalA0", "Palettized (no alpha)"),
    ("BlpPalA1", "Palettized (1-bit alpha)"),
    ("BlpPalA4", "Palettized (4-bit alpha)"),
    ("BlpPalA8", "Palettized (full alpha)"),
    ("BlpDxt1A0", "Dxt1 (no alpha)"),
    ("BlpDxt1A1", "Dxt1 (alpha)"),
    ("BlpDxt3", "Dxt3"),
    ("BlpDxt5", "Dxt5"),
    ("BlpBgra", "Bgra"),
    ("PngPal", "Palettized (no alpha)"),
    ("PngPalMask", "Palettized (with transparency)"),
    ("PngRgb", "Rgb"),
    ("PngRgba", "Rgba"),
];

impl FormatID {
    pub fn is_blp(&self) -> bool {
        matches!(*self as u8, 1..=9)
    }

    pub fn is_png(&self) -> bool {
        matches!(*self as u8, 10..=13)
    }

    pub fn name(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(name, _)| name)
            .unwrap_or_default()
    }

    pub fn description(&self) -> &'static str {
        FORMAT_INFO
            .get(*self as usize)
            .map(|&(_, description)| description)
            .unwrap_or_default()
    }
}

impl From<usize> for FormatID {
    fn from(value: usize) -> Self {
        use FormatID::*;
        match value {
            1 => BlpPalA0,
            2 => BlpPalA1,
            3 => BlpPalA4,
            4 => BlpPalA8,
            5 => BlpDxt1A0,
            6 => BlpDxt1A1,
            7 => BlpDxt3,
            8 => BlpDxt5,
            9 => BlpBgra,
            10 => PngPal,
            11 => PngPalMask,
            12 => PngRgb,
            13 => PngRgba,
            14 => FormatCount,
            _ => Unspecified, // 默认 fallback
        }
    }
}

// 默认格式映射规则（用于显示）
pub static FORMAT_RULES: &[(FormatID, FormatID)] = &[
    (FormatID::Unspecified, FormatID::Unspecified),
    (FormatID::BlpPalA0, FormatID::PngPal),
    (FormatID::BlpPalA1, FormatID::PngPalMask),
    (FormatID::BlpPalA4, FormatID::PngRgba),
    (FormatID::BlpPalA8, FormatID::PngRgba),
    (FormatID::BlpDxt1A0, FormatID::PngRgb),
    (FormatID::BlpDxt1A1, FormatID::PngRgba),
    (FormatID::BlpDxt3, FormatID::PngRgba),
    (FormatID::BlpDxt5, FormatID::PngRgba),
    (FormatID::BlpBgra, FormatID::PngRgba),
    (FormatID::PngPal, FormatID::BlpPalA0),
    (FormatID::PngPalMask, FormatID::BlpPalA1),
    (FormatID::PngRgb, FormatID::BlpDxt1A0),
    (FormatID::PngRgba, FormatID::BlpDxt3),
];

// ============================================================================
// 错误处理
// ============================================================================

#[derive(Debug)]
pub enum AppError {
    InvalidArgs,
    InvalidAlphaThreshold,
    InvalidFormatString,
    InvalidConversionRule,
    ExclusiveOptions,
    FilenameMissing,
    ProcessingFailed(std::path::PathBuf),
    GlobError(String),
    FileError(std::io::Error),
    NotBlp,
    VersionError,
    Error(&'static str),
    Patch,
    WriteError(png::EncodingError),
    ReadError(png::DecodingError),
    LiqError(imagequant::liq_error),
    ListFormatsRequested,
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::FileError(e) => write!(f, "File error: {}", e),
            AppError::NotBlp => write!(f, "Not a valid BLP file."),
            AppError::VersionError => write!(f, "Unsupported version"),
            AppError::Error(msg) => write!(f, "Error: {}", msg),
            AppError::Patch => write!(f, "Patch error"),
            AppError::WriteError(e) => write!(f, "Encodding error: {}", e),
            AppError::ReadError(e) => write!(f, "Decoding error: {}", e),
            AppError::LiqError(e) => write!(f, "Image quant fail: {}", e),
            AppError::InvalidArgs => write!(f, "Invalid arguments."),
            AppError::InvalidAlphaThreshold => write!(f, "ERROR: Alpha threshold must be between 0 and 255."),
            AppError::InvalidFormatString => write!(f, "ERROR: Invalid format string provided."),
            AppError::InvalidConversionRule => write!(f, "ERROR: Invalid conversion rule format. Expected SOURCE_FORMAT=TARGET_FORMAT."),
            AppError::ExclusiveOptions => write!(f, "ERROR: -r and -h are exclusive."),
            AppError::FilenameMissing => write!(f, "ERROR: Filename argument missing."),
            AppError::ProcessingFailed(path) => write!(f, "ERROR: Processing of '{}' failed.", path.display()),
            AppError::GlobError(msg) => write!(f, "ERROR: Glob pattern error: {}.", msg),
            AppError::ListFormatsRequested => write!(f, "List formats requested"),
        }
    }
}

impl std::error::Error for AppError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            AppError::FileError(e) => Some(e),
            AppError::WriteError(e) => Some(e),
            AppError::ReadError(e) => Some(e),
            AppError::LiqError(e) => Some(e),
            _ => None,
        }
    }
}

impl From<std::io::Error> for AppError {
    fn from(e: std::io::Error) -> Self {
        AppError::FileError(e)
    }
}

impl From<png::EncodingError> for AppError {
    fn from(e: png::EncodingError) -> Self {
        AppError::WriteError(e)
    }
}

impl From<png::DecodingError> for AppError {
    fn from(e: png::DecodingError) -> Self {
        AppError::ReadError(e)
    }
}

impl From<imagequant::liq_error> for AppError {
    fn from(e: imagequant::liq_error) -> Self {
        AppError::LiqError(e)
    }
}

impl From<glob::GlobError> for AppError {
    fn from(err: glob::GlobError) -> Self {
        AppError::GlobError(err.to_string())
    }
}

impl From<glob::PatternError> for AppError {
    fn from(err: glob::PatternError) -> Self {
        AppError::GlobError(err.to_string())
    }
}

// ============================================================================
// 配置管理
// ============================================================================

#[derive(Debug, Clone)]
pub struct Config {
    // 图像处理配置
    pub alpha_threshold: u8,
    pub gamma_factor: f32,
    pub target_format: FormatID,
    pub no_mips: bool,

    // 输出配置
    pub verbose: bool,
    pub multi_file_mode: bool,
    pub pause_at_end: bool,
    pub pause_on_error: bool,

    // 特殊模式
    pub clothing_option_set: bool,
    pub character_option_set: bool,
    pub info_mode: bool,

    // 文件处理
    pub source_files: Vec<PathBuf>,
    pub dest_file: Option<PathBuf>,

    // 格式映射规则
    pub format_rules: Vec<(FormatID, FormatID)>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            alpha_threshold: 0x80,
            gamma_factor: 1.0,
            target_format: FormatID::Unspecified,
            no_mips: false,
            verbose: false,
            multi_file_mode: false,
            pause_at_end: false,
            pause_on_error: false,
            clothing_option_set: false,
            character_option_set: false,
            info_mode: false,
            source_files: Vec::new(),
            dest_file: None,
            format_rules: Vec::new(),
        }
    }
}

impl Config {
    /// 从命令行参数创建配置
    pub fn from_args() -> Result<Self, AppError> {
        let args: Vec<String> = std::env::args().collect();
        Self::parse_args(&args)
    }

    /// 解析命令行参数
    fn parse_args(args: &[String]) -> Result<Self, AppError> {
        if args.len() == 1 {
            return Err(AppError::InvalidArgs);
        }

        let mut config = Self::default();
        let mut i = 1;

        while i < args.len() {
            let arg = &args[i];

            if arg.starts_with('-') || arg.starts_with('/') {
                i += Self::parse_option(&mut config, arg)?;
            } else {
                // 文件参数
                config.source_files.push(PathBuf::from(arg));
                i += 1;
            }
        }

        // 验证配置
        config.validate()?;

        Ok(config)
    }

    /// 解析单个选项
    fn parse_option(config: &mut Config, arg: &str) -> Result<usize, AppError> {
        let option_char = arg.chars().nth(1)
            .ok_or(AppError::InvalidArgs)?
            .to_ascii_lowercase();
        let opt_value = &arg[2..];

        match option_char {
            'a' => {
                config.alpha_threshold = opt_value.parse()
                    .map_err(|_| AppError::InvalidAlphaThreshold)?;
                Ok(1)
            }
            'v' => {
                config.verbose = true;
                Ok(1)
            }
            'f' => {
                config.target_format = Self::parse_format(opt_value)?;
                Ok(1)
            }
            'n' => {
                config.no_mips = true;
                Ok(1)
            }
            'm' => {
                config.multi_file_mode = true;
                Ok(1)
            }
            'i' => {
                config.info_mode = true;
                config.verbose = true;
                config.multi_file_mode = true;
                Ok(1)
            }
            'p' => {
                config.pause_at_end = true;
                Ok(1)
            }
            'e' => {
                config.pause_on_error = true;
                Ok(1)
            }
            'h' => {
                config.character_option_set = true;
                Self::apply_character_mappings(config);
                Ok(1)
            }
            'r' => {
                config.clothing_option_set = true;
                Self::apply_clothing_mappings(config);
                Ok(1)
            }
            'u' => {
                Self::parse_mapping_rule(config, opt_value)?;
                Ok(1)
            }
            'l' => {
                return Err(AppError::ListFormatsRequested);
            }
            '?' => {
                return Err(AppError::Error("Help requested"));
            }
            _ => Err(AppError::InvalidArgs)
        }
    }

    /// 解析格式字符串
    fn parse_format(format_str: &str) -> Result<FormatID, AppError> {
        use FormatID::*;

        match format_str.to_lowercase().as_str() {
            "pngrgb" => Ok(PngRgb),
            "pngrgba" => Ok(PngRgba),
            "pngpal" => Ok(PngPal),
            "pngpalmask" => Ok(PngPalMask),
            "blppala0" => Ok(BlpPalA0),
            "blppala1" => Ok(BlpPalA1),
            "blppala4" => Ok(BlpPalA4),
            "blppala8" => Ok(BlpPalA8),
            "blpdxt1a0" => Ok(BlpDxt1A0),
            "blpdxt1a1" => Ok(BlpDxt1A1),
            "blpdxt3" => Ok(BlpDxt3),
            "blpdxt5" => Ok(BlpDxt5),
            "blpbgra" => Ok(BlpBgra),
            _ => Err(AppError::InvalidFormatString)
        }
    }

    /// 解析映射规则
    fn parse_mapping_rule(config: &mut Config, rule: &str) -> Result<(), AppError> {
        let parts: Vec<&str> = rule.splitn(2, '=').collect();
        if parts.len() != 2 {
            return Err(AppError::InvalidConversionRule);
        }

        let from = Self::parse_format(parts[0])?;
        let to = Self::parse_format(parts[1])?;

        config.format_rules.push((from, to));
        Ok(())
    }

    /// 应用角色纹理映射
    fn apply_character_mappings(config: &mut Config) {
        use FormatID::*;
        config.format_rules.extend_from_slice(&[
            (PngPalMask, BlpPalA0),
            (PngRgb, BlpPalA0),
            (PngRgba, BlpPalA0),
        ]);
    }

    /// 应用服装纹理映射
    fn apply_clothing_mappings(config: &mut Config) {
        use FormatID::*;
        config.format_rules.extend_from_slice(&[
            (PngRgb, BlpPalA0),
            (PngRgba, BlpPalA8),
            (BlpPalA0, PngRgb),
            (BlpPalA1, PngRgba),
            (BlpPalA4, PngRgba),
            (BlpPalA8, PngRgba),
        ]);
    }

    /// 验证配置的有效性
    fn validate(&mut self) -> Result<(), AppError> {
        // 检查互斥选项
        if self.clothing_option_set && self.character_option_set {
            return Err(AppError::ExclusiveOptions);
        }

        // 检查文件参数
        if self.source_files.is_empty() {
            return Err(AppError::FilenameMissing);
        }

        // 处理目标文件
        if !self.multi_file_mode && self.source_files.len() > 1 {
            self.dest_file = self.source_files.pop();
        }

        Ok(())
    }

    /// 获取格式映射
    pub fn get_format_mapping(&self, from: FormatID) -> FormatID {
        self.format_rules.iter()
            .find(|(source, _)| *source == from)
            .map(|(_, target)| *target)
            .unwrap_or(from)
    }
}
