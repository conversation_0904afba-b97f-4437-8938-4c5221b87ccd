// 新的配置管理模块 - 替代全局静态变量
use crate::cfg::FormatID;
use std::path::PathBuf;
use thiserror::Error;

#[derive(Debug, Clone)]
pub struct Config {
    // 图像处理配置
    pub alpha_threshold: u8,
    pub gamma_factor: f32,
    pub target_format: FormatID,
    pub no_mips: bool,
    
    // 输出配置
    pub verbose: bool,
    pub multi_file_mode: bool,
    pub pause_at_end: bool,
    pub pause_on_error: bool,
    
    // 特殊模式
    pub clothing_option_set: bool,
    pub character_option_set: bool,
    pub info_mode: bool,
    
    // 文件处理
    pub source_files: Vec<PathBuf>,
    pub dest_file: Option<PathBuf>,
    
    // 格式映射规则
    pub format_rules: Vec<(FormatID, FormatID)>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            alpha_threshold: 0x80,
            gamma_factor: 1.0,
            target_format: FormatID::Unspecified,
            no_mips: false,
            verbose: false,
            multi_file_mode: false,
            pause_at_end: false,
            pause_on_error: false,
            clothing_option_set: false,
            character_option_set: false,
            info_mode: false,
            source_files: Vec::new(),
            dest_file: None,
            format_rules: Vec::new(),
        }
    }
}

impl Config {
    /// 从命令行参数创建配置
    pub fn from_args() -> Result<Self, ConfigError> {
        let args: Vec<String> = std::env::args().collect();
        Self::parse_args(&args)
    }
    
    /// 解析命令行参数
    fn parse_args(args: &[String]) -> Result<Self, ConfigError> {
        if args.len() == 1 {
            return Err(ConfigError::NoArguments);
        }
        
        let mut config = Self::default();
        let mut i = 1;
        
        while i < args.len() {
            let arg = &args[i];
            
            if arg.starts_with('-') || arg.starts_with('/') {
                i += Self::parse_option(&mut config, arg, &args, i)?;
            } else {
                // 文件参数
                config.source_files.push(PathBuf::from(arg));
                i += 1;
            }
        }
        
        // 验证配置
        config.validate()?;
        
        Ok(config)
    }
    
    /// 解析单个选项
    fn parse_option(
        config: &mut Config, 
        arg: &str, 
        args: &[String], 
        current_index: usize
    ) -> Result<usize, ConfigError> {
        let option_char = arg.chars().nth(1)
            .ok_or(ConfigError::InvalidOption(arg.to_string()))?
            .to_ascii_lowercase();
        let opt_value = &arg[2..];
        
        match option_char {
            'a' => {
                config.alpha_threshold = opt_value.parse()
                    .map_err(|_| ConfigError::InvalidAlphaThreshold)?;
                Ok(1)
            }
            'v' => {
                config.verbose = true;
                Ok(1)
            }
            'f' => {
                config.target_format = Self::parse_format(opt_value)?;
                Ok(1)
            }
            'n' => {
                config.no_mips = true;
                Ok(1)
            }
            'm' => {
                config.multi_file_mode = true;
                Ok(1)
            }
            'i' => {
                config.info_mode = true;
                config.verbose = true;
                config.multi_file_mode = true;
                Ok(1)
            }
            'p' => {
                config.pause_at_end = true;
                Ok(1)
            }
            'e' => {
                config.pause_on_error = true;
                Ok(1)
            }
            'h' => {
                config.character_option_set = true;
                Self::apply_character_mappings(config);
                Ok(1)
            }
            'r' => {
                config.clothing_option_set = true;
                Self::apply_clothing_mappings(config);
                Ok(1)
            }
            'u' => {
                Self::parse_mapping_rule(config, opt_value)?;
                Ok(1)
            }
            '?' => {
                return Err(ConfigError::HelpRequested);
            }
            _ => Err(ConfigError::InvalidOption(arg.to_string()))
        }
    }
    
    /// 解析格式字符串
    fn parse_format(format_str: &str) -> Result<FormatID, ConfigError> {
        use FormatID::*;

        match format_str.to_lowercase().as_str() {
            "pngrgb" => Ok(PngRgb),
            "pngrgba" => Ok(PngRgba),
            "pngpal" => Ok(PngPal),
            "pngpalmask" => Ok(PngPalMask),
            "blppala0" => Ok(BlpPalA0),
            "blppala1" => Ok(BlpPalA1),
            "blppala4" => Ok(BlpPalA4),
            "blppala8" => Ok(BlpPalA8),
            "blpdxt1a0" => Ok(BlpDxt1A0),
            "blpdxt1a1" => Ok(BlpDxt1A1),
            "blpdxt3" => Ok(BlpDxt3),
            "blpdxt5" => Ok(BlpDxt5),
            "blpbgra" => Ok(BlpBgra),
            _ => Err(ConfigError::InvalidFormat(format_str.to_string()))
        }
    }
    
    /// 解析映射规则
    fn parse_mapping_rule(config: &mut Config, rule: &str) -> Result<(), ConfigError> {
        let parts: Vec<&str> = rule.splitn(2, '=').collect();
        if parts.len() != 2 {
            return Err(ConfigError::InvalidMappingRule(rule.to_string()));
        }
        
        let from = Self::parse_format(parts[0])?;
        let to = Self::parse_format(parts[1])?;
        
        config.format_rules.push((from, to));
        Ok(())
    }
    
    /// 应用角色纹理映射
    fn apply_character_mappings(config: &mut Config) {
        use FormatID::*;
        config.format_rules.extend_from_slice(&[
            (PngPalMask, BlpPalA0),
            (PngRgb, BlpPalA0),
            (PngRgba, BlpPalA0),
        ]);
    }
    
    /// 应用服装纹理映射
    fn apply_clothing_mappings(config: &mut Config) {
        use FormatID::*;
        config.format_rules.extend_from_slice(&[
            (PngRgb, BlpPalA0),
            (PngRgba, BlpPalA8),
            (BlpPalA0, PngRgb),
            (BlpPalA1, PngRgba),
            (BlpPalA4, PngRgba),
            (BlpPalA8, PngRgba),
        ]);
    }
    
    /// 验证配置的有效性
    fn validate(&mut self) -> Result<(), ConfigError> {
        // 检查互斥选项
        if self.clothing_option_set && self.character_option_set {
            return Err(ConfigError::ExclusiveOptions);
        }
        
        // 检查文件参数
        if self.source_files.is_empty() {
            return Err(ConfigError::NoSourceFiles);
        }
        
        // 处理目标文件
        if !self.multi_file_mode && self.source_files.len() > 1 {
            self.dest_file = self.source_files.pop().map(|p| p);
        }
        
        Ok(())
    }
    
    /// 获取格式映射
    pub fn get_format_mapping(&self, from: FormatID) -> FormatID {
        self.format_rules.iter()
            .find(|(source, _)| *source == from)
            .map(|(_, target)| *target)
            .unwrap_or(from)
    }
}

#[derive(Debug)]
pub enum ConfigError {
    NoArguments,
    InvalidOption(String),
    InvalidAlphaThreshold,
    InvalidFormat(String),
    InvalidMappingRule(String),
    ExclusiveOptions,
    NoSourceFiles,
    HelpRequested,
}

impl std::fmt::Display for ConfigError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConfigError::NoArguments => write!(f, "No arguments provided"),
            ConfigError::InvalidOption(opt) => write!(f, "Invalid option: {}", opt),
            ConfigError::InvalidAlphaThreshold => write!(f, "Invalid alpha threshold value"),
            ConfigError::InvalidFormat(fmt) => write!(f, "Invalid format: {}", fmt),
            ConfigError::InvalidMappingRule(rule) => write!(f, "Invalid mapping rule: {}", rule),
            ConfigError::ExclusiveOptions => write!(f, "Exclusive options cannot be used together"),
            ConfigError::NoSourceFiles => write!(f, "No source files specified"),
            ConfigError::HelpRequested => write!(f, "Help requested"),
        }
    }
}

impl std::error::Error for ConfigError {}
