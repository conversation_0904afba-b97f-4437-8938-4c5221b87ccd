// 处理管道 - 统一的图像转换流程
use std::path::{Path, PathBuf};

use crate::config::Config;
use crate::image::{Image, FormatRegistry};
use crate::config::{AppError, FormatID};

/// 处理结果
#[derive(Debug)]
pub struct ProcessResult {
    pub input_path: PathBuf,
    pub output_path: PathBuf,
    pub input_format: FormatID,
    pub output_format: FormatID,
    pub processing_time: std::time::Duration,
    pub file_size_before: u64,
    pub file_size_after: u64,
}

/// 图像处理管道
pub struct ProcessingPipeline {
    registry: FormatRegistry,
    config: Config,
}

impl ProcessingPipeline {
    /// 创建新的处理管道
    pub fn new(config: Config) -> Self {
        let registry = FormatRegistry::new();
        Self { registry, config }
    }
    
    /// 处理单个文件
    pub fn process_file(
        &self, 
        input_path: &Path, 
        output_path: Option<&Path>
    ) -> Result<ProcessResult, AppError> {
        let start_time = std::time::Instant::now();
        
        // 1. 检测输入格式
        let input_ext = self.get_file_extension(input_path)?;
        let input_format_handler = self.registry.get_format(&input_ext)
            .ok_or_else(|| AppError::Error("Unsupported file format"))?;
        
        // 2. 获取文件大小
        let file_size_before = std::fs::metadata(input_path)?.len();
        
        if self.config.verbose {
            println!("Processing: {}", input_path.display());
        }
        
        // 3. 加载图像
        let mut image = input_format_handler.load(input_path, &self.config)?;
        let input_format = self.detect_format_id(&image, &input_ext);
        
        if self.config.info_mode {
            self.print_image_info(&image, input_format);
            return Ok(ProcessResult {
                input_path: input_path.to_path_buf(),
                output_path: input_path.to_path_buf(),
                input_format,
                output_format: input_format,
                processing_time: start_time.elapsed(),
                file_size_before,
                file_size_after: file_size_before,
            });
        }
        
        // 4. 确定输出路径和格式
        let (output_path, output_format) = self.determine_output(
            input_path, 
            output_path, 
            input_format
        )?;
        
        // 5. 应用格式转换
        self.apply_format_conversion(&mut image, input_format, output_format)?;
        
        // 6. 保存图像
        let output_ext = self.get_file_extension(&output_path)?;
        let output_format_handler = self.registry.get_format(&output_ext)
            .ok_or_else(|| AppError::Error("Unsupported output format"))?;
        output_format_handler.save(&image, &output_path, &self.config)?;
        
        // 7. 获取输出文件大小
        let file_size_after = std::fs::metadata(&output_path)?.len();
        
        let processing_time = start_time.elapsed();
        
        if self.config.verbose {
            println!(
                "Converted: {} ({}) -> {} ({}) in {:?}",
                input_path.display(),
                input_format.name(),
                output_path.display(),
                output_format.name(),
                processing_time
            );
        }
        
        Ok(ProcessResult {
            input_path: input_path.to_path_buf(),
            output_path,
            input_format,
            output_format,
            processing_time,
            file_size_before,
            file_size_after,
        })
    }
    
    /// 批量处理文件
    pub fn process_batch(&self, patterns: &[String]) -> Result<Vec<ProcessResult>, AppError> {
        let mut results = Vec::new();
        
        for pattern in patterns {
            for entry in glob::glob(pattern)? {
                let path = entry?;
                if path.is_file() {
                    match self.process_file(&path, None) {
                        Ok(result) => results.push(result),
                        Err(e) => {
                            eprintln!("Error processing {}: {}", path.display(), e);
                            if !self.config.pause_on_error {
                                continue;
                            } else {
                                return Err(e);
                            }
                        }
                    }
                }
            }
        }
        
        Ok(results)
    }
    
    /// 并行批量处理（可选功能）
    #[cfg(feature = "parallel")]
    pub fn process_batch_parallel(&self, patterns: &[String]) -> Result<Vec<ProcessResult>, AppError> {
        use rayon::prelude::*;
        
        let files: Result<Vec<_>, _> = patterns.iter()
            .flat_map(|pattern| glob::glob(pattern).unwrap())
            .collect::<Result<Vec<_>, _>>()?;
        
        let results: Result<Vec<_>, _> = files.into_par_iter()
            .filter(|path| path.is_file())
            .map(|path| self.process_file(&path, None))
            .collect();
        
        results
    }
    
    /// 获取文件扩展名
    fn get_file_extension(&self, path: &Path) -> Result<String, AppError> {
        path.extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_lowercase())
            .ok_or_else(|| AppError::Error("Invalid file extension"))
    }
    

    
    /// 检测FormatID
    fn detect_format_id(&self, image: &Image, ext: &str) -> FormatID {
        // 根据图像属性和扩展名检测具体的FormatID
        // 这里需要实现具体的检测逻辑
        FormatID::Unspecified // 占位符
    }
    
    /// 确定输出路径和格式
    fn determine_output(
        &self,
        input_path: &Path,
        output_path: Option<&Path>,
        input_format: FormatID,
    ) -> Result<(PathBuf, FormatID), AppError> {
        let output_format = if self.config.target_format != FormatID::Unspecified {
            self.config.target_format
        } else {
            self.config.get_format_mapping(input_format)
        };
        
        let output_path = if let Some(path) = output_path {
            path.to_path_buf()
        } else {
            self.generate_output_path(input_path, input_format, output_format)?
        };
        
        Ok((output_path, output_format))
    }
    
    /// 生成输出路径
    fn generate_output_path(
        &self,
        input_path: &Path,
        input_format: FormatID,
        output_format: FormatID,
    ) -> Result<PathBuf, AppError> {
        let stem = input_path.file_stem()
            .and_then(|s| s.to_str())
            .ok_or(AppError::Error("Invalid filename"))?;
        
        let same_type = (input_format.is_blp() && output_format.is_blp()) ||
                       (input_format.is_png() && output_format.is_png());
        
        let ext = if output_format.is_blp() { "blp" } else { "png" };
        let suffix = if same_type { "_" } else { "" };
        
        Ok(input_path.with_file_name(format!("{}{}.{}", stem, suffix, ext)))
    }
    
    /// 应用格式转换
    fn apply_format_conversion(
        &self,
        image: &mut Image,
        input_format: FormatID,
        output_format: FormatID,
    ) -> Result<(), AppError> {
        // 根据输出格式要求转换图像
        match output_format {
            FormatID::PngRgb => image.to_rgb()?,
            FormatID::PngRgba => image.to_rgba()?,
            FormatID::PngPal | FormatID::PngPalMask => {
                let has_alpha = output_format == FormatID::PngPalMask;
                image.to_indexed(has_alpha)?;
            }
            // BLP格式的转换逻辑
            _ if output_format.is_blp() => {
                // 根据具体的BLP格式进行转换
                // 这里需要实现具体的BLP转换逻辑
            }
            _ => {} // 其他格式
        }
        
        Ok(())
    }
    
    /// 打印图像信息
    fn print_image_info(&self, image: &Image, format: FormatID) {
        println!("\t{}x{}", image.width, image.height);
        println!("\tFormat = {} ({})", format.name(), format.description());
        
        if let Some(palette) = &image.palette {
            println!("\t{} palette entries", palette.len());
        }
        
        if image.format.has_alpha() {
            println!("\tHas alpha channel");
        }
    }
}
