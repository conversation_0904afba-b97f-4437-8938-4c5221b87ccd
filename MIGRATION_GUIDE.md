# 🔄 BLP2PNG 架构迁移指南

## 概述

本指南帮助你从旧的架构迁移到新的重构架构。新架构提供了更好的模块化、可测试性和可扩展性。

## 🏗️ 架构对比

### 旧架构问题
```rust
// 旧方式：全局状态
static S_BY_ALPHA_THRESHOLD: Lazy<RwLock<u8>> = Lazy::new(|| RwLock::new(0x80));

// 旧方式：紧耦合
let mut image = MemImage::default();
image.load_from_blp(path)?;
```

### 新架构优势
```rust
// 新方式：配置对象
let config = Config {
    alpha_threshold: 0x80,
    verbose: true,
    ..Default::default()
};

// 新方式：管道处理
let pipeline = ProcessingPipeline::new(config);
let result = pipeline.process_file(input_path, output_path)?;
```

## 📋 迁移步骤

### 第1步：更新主程序

**旧代码 (main.rs):**
```rust
fn main() -> Result<(), AppError> {
    let config = Config::parse_args()?;
    let result = run(&config);
    // ... 错误处理
}
```

**新代码 (main_refactored.rs):**
```rust
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::from_args()?;
    let pipeline = ProcessingPipeline::new(config.clone());
    let result = run_with_pipeline(&config)?;
    // ... 改进的错误处理
}
```

### 第2步：配置管理迁移

**旧方式：**
```rust
// 全局函数调用
set_alpha_threshold(128);
set_verbose(true);
let threshold = get_alpha_threshold();
```

**新方式：**
```rust
// 配置对象
let mut config = Config::default();
config.alpha_threshold = 128;
config.verbose = true;
// 通过参数传递，不再使用全局状态
```

### 第3步：图像处理迁移

**旧方式：**
```rust
let mut image = MemImage::default();
let format = image.load_from_blp(path)?;
image.save(output_path, target_format)?;
```

**新方式：**
```rust
let registry = FormatRegistry::new();
let format_handler = registry.get_format("blp").unwrap();
let image = format_handler.load(path, &config)?;
format_handler.save(&image, output_path, &config)?;
```

### 第4步：批处理迁移

**旧方式：**
```rust
for pattern in &config.source_files {
    for entry in glob::glob(pattern)? {
        let path = entry?;
        process_file(path.to_str().unwrap(), None)?;
    }
}
```

**新方式：**
```rust
let pipeline = ProcessingPipeline::new(config);
let patterns: Vec<String> = config.source_files.iter()
    .map(|p| p.to_string_lossy().to_string())
    .collect();
let results = pipeline.process_batch(&patterns)?;
```

## 🔧 API 变更

### 配置API
| 旧API | 新API |
|-------|-------|
| `set_alpha_threshold(value)` | `config.alpha_threshold = value` |
| `get_alpha_threshold()` | `config.alpha_threshold` |
| `set_verbose(bool)` | `config.verbose = bool` |
| `get_verbose()` | `config.verbose` |

### 图像处理API
| 旧API | 新API |
|-------|-------|
| `MemImage::load_from_blp()` | `BlpFormat::load()` |
| `MemImage::save()` | `ImageFormat::save()` |
| 直接调用格式函数 | 通过`FormatRegistry`获取处理器 |

### 错误处理API
| 旧API | 新API |
|-------|-------|
| `AppError` | `Box<dyn std::error::Error>` |
| 简单错误信息 | 结构化错误链 |

## 🚀 新功能

### 1. 处理管道
```rust
let pipeline = ProcessingPipeline::new(config);

// 单文件处理
let result = pipeline.process_file(input, output)?;

// 批量处理
let results = pipeline.process_batch(&patterns)?;

// 并行处理（可选）
#[cfg(feature = "parallel")]
let results = pipeline.process_batch_parallel(&patterns)?;
```

### 2. 格式注册表
```rust
let mut registry = FormatRegistry::new();

// 注册自定义格式
registry.register("custom", Box::new(CustomFormat));

// 获取支持的格式
let extensions = registry.supported_extensions();
```

### 3. 改进的错误处理
```rust
// 结构化错误信息
match result {
    Err(e) => {
        eprintln!("Error: {}", e);
        
        // 错误链追踪
        let mut source = e.source();
        while let Some(err) = source {
            eprintln!("Caused by: {}", err);
            source = err.source();
        }
    }
    Ok(_) => println!("Success!"),
}
```

### 4. 配置文件支持（计划中）
```rust
// 从文件加载配置
let config = Config::from_file("blp2png.toml")?;

// 保存配置到文件
config.save_to_file("blp2png.toml")?;
```

## 🧪 测试迁移

### 旧测试方式
```rust
#[test]
fn test_conversion() {
    set_verbose(true);
    let mut image = MemImage::default();
    // ... 测试逻辑
}
```

### 新测试方式
```rust
#[test]
fn test_conversion() {
    let config = Config {
        verbose: true,
        ..Default::default()
    };
    let pipeline = ProcessingPipeline::new(config);
    // ... 测试逻辑，更容易模拟和验证
}
```

## 📦 依赖更新

在`Cargo.toml`中添加新依赖：
```toml
[dependencies]
thiserror = "1.0"
anyhow = "1.0"
# 可选：并行处理
rayon = { version = "1.7", optional = true }

[features]
parallel = ["rayon"]
```

## 🔄 渐进式迁移策略

1. **阶段1**: 保持旧代码，添加新模块
2. **阶段2**: 创建适配器层，逐步迁移功能
3. **阶段3**: 完全切换到新架构
4. **阶段4**: 移除旧代码和全局状态

## ⚠️ 注意事项

1. **向后兼容性**: 新架构通过`apply_config_to_globals()`保持临时兼容
2. **性能**: 新架构可能有轻微的性能开销，但提供了更好的可维护性
3. **内存使用**: 配置对象会增加少量内存使用
4. **线程安全**: 新架构天然线程安全，不需要全局锁

## 🎯 迁移检查清单

- [ ] 更新主程序使用新的配置系统
- [ ] 替换全局状态调用为配置对象
- [ ] 使用处理管道替代直接函数调用
- [ ] 更新错误处理逻辑
- [ ] 迁移测试代码
- [ ] 更新文档和示例
- [ ] 性能基准测试
- [ ] 移除旧的全局状态代码

## 📚 更多资源

- [架构优化文档](ARCHITECTURE_OPTIMIZATION.md)
- [性能优化报告](PERFORMANCE_OPTIMIZATIONS.md)
- [使用示例](examples/usage_example.rs)
- [API文档](docs/api.md)

通过遵循这个迁移指南，你可以安全地将现有代码迁移到新的架构，享受更好的代码组织和可维护性。
