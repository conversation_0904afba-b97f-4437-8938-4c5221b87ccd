// 格式处理模块 - 实现具体的图像格式处理器
use std::path::Path;
use crate::config::{AppError, FormatID};
use crate::config::Config;
use crate::image::{Image, ImageFormat, PixelFormat, CompressionFormat};

/// BLP格式处理器
pub struct BlpFormat;

impl ImageFormat for BlpFormat {
    fn load(&self, path: &Path, config: &Config) -> Result<Image, AppError> {
        // 使用现有的BLP加载逻辑，但转换为新的Image结构
        let mut mem_image = crate::img::MemImage::default();
        let format_id = crate::blp::load_from_blp(&mut mem_image, path)?;
        
        // 转换为新的Image结构
        let pixel_format = Self::format_id_to_pixel_format(format_id);
        
        Ok(Image {
            data: mem_image.m_buffer,
            width: mem_image.m_width,
            height: mem_image.m_height,
            format: pixel_format,
            palette: mem_image.m_palette,
        })
    }
    
    fn save(&self, image: &Image, path: &Path, config: &Config) -> Result<(), AppError> {
        // 转换回MemImage格式以使用现有的保存逻辑
        let mut mem_image = crate::img::MemImage {
            m_buffer: image.data.clone(),
            has_alpha: image.format.has_alpha(),
            is_palettized: image.format.is_indexed(),
            m_width: image.width,
            m_height: image.height,
            m_palette: image.palette.clone(),
        };
        
        // 确定目标格式
        let target_format = if config.target_format != FormatID::Unspecified {
            config.target_format
        } else {
            Self::pixel_format_to_format_id(&image.format)
        };
        
        crate::blp::save_to_blp(mem_image, path, target_format)
    }
    
    fn supports_extension(&self, ext: &str) -> bool {
        ext.eq_ignore_ascii_case("blp")
    }
    
    fn format_name(&self) -> &'static str {
        "BLP"
    }
    
    fn supported_format_ids(&self) -> &[FormatID] {
        &[
            FormatID::BlpPalA0,
            FormatID::BlpPalA1,
            FormatID::BlpPalA4,
            FormatID::BlpPalA8,
            FormatID::BlpDxt1A0,
            FormatID::BlpDxt1A1,
            FormatID::BlpDxt3,
            FormatID::BlpDxt5,
            FormatID::BlpBgra,
        ]
    }
}

impl BlpFormat {
    fn format_id_to_pixel_format(format_id: FormatID) -> PixelFormat {
        match format_id {
            FormatID::BlpPalA0 => PixelFormat::Indexed { 
                has_alpha: false, 
                bits_per_pixel: 8 
            },
            FormatID::BlpPalA1 | FormatID::BlpPalA4 | FormatID::BlpPalA8 => {
                PixelFormat::Indexed { 
                    has_alpha: true, 
                    bits_per_pixel: 8 
                }
            },
            FormatID::BlpDxt1A0 => PixelFormat::Compressed {
                format: CompressionFormat::Dxt1,
                has_alpha: false,
            },
            FormatID::BlpDxt1A1 => PixelFormat::Compressed {
                format: CompressionFormat::Dxt1,
                has_alpha: true,
            },
            FormatID::BlpDxt3 => PixelFormat::Compressed {
                format: CompressionFormat::Dxt3,
                has_alpha: true,
            },
            FormatID::BlpDxt5 => PixelFormat::Compressed {
                format: CompressionFormat::Dxt5,
                has_alpha: true,
            },
            FormatID::BlpBgra => PixelFormat::Rgba,
            _ => PixelFormat::Rgba, // 默认
        }
    }
    
    fn pixel_format_to_format_id(pixel_format: &PixelFormat) -> FormatID {
        match pixel_format {
            PixelFormat::Indexed { has_alpha: false, .. } => FormatID::BlpPalA0,
            PixelFormat::Indexed { has_alpha: true, .. } => FormatID::BlpPalA8,
            PixelFormat::Compressed { format: CompressionFormat::Dxt1, has_alpha: false } => {
                FormatID::BlpDxt1A0
            },
            PixelFormat::Compressed { format: CompressionFormat::Dxt1, has_alpha: true } => {
                FormatID::BlpDxt1A1
            },
            PixelFormat::Compressed { format: CompressionFormat::Dxt3, .. } => {
                FormatID::BlpDxt3
            },
            PixelFormat::Compressed { format: CompressionFormat::Dxt5, .. } => {
                FormatID::BlpDxt5
            },
            PixelFormat::Rgb => FormatID::BlpBgra, // 转换为BGRA
            PixelFormat::Rgba => FormatID::BlpBgra,
        }
    }
}

/// PNG格式处理器
pub struct PngFormat;

impl ImageFormat for PngFormat {
    fn load(&self, path: &Path, config: &Config) -> Result<Image, AppError> {
        // 使用现有的PNG加载逻辑
        let mut mem_image = crate::img::MemImage::default();
        let format_id = crate::png::load_from_png(&mut mem_image, path)?;
        
        // 转换为新的Image结构
        let pixel_format = Self::format_id_to_pixel_format(format_id);
        
        Ok(Image {
            data: mem_image.m_buffer,
            width: mem_image.m_width,
            height: mem_image.m_height,
            format: pixel_format,
            palette: mem_image.m_palette,
        })
    }
    
    fn save(&self, image: &Image, path: &Path, config: &Config) -> Result<(), AppError> {
        // 转换回MemImage格式
        let mem_image = crate::img::MemImage {
            m_buffer: image.data.clone(),
            has_alpha: image.format.has_alpha(),
            is_palettized: image.format.is_indexed(),
            m_width: image.width,
            m_height: image.height,
            m_palette: image.palette.clone(),
        };
        
        // 确定目标格式
        let target_format = if config.target_format != FormatID::Unspecified {
            config.target_format
        } else {
            Self::pixel_format_to_format_id(&image.format)
        };
        
        crate::png::save_to_png(mem_image, path, target_format)
    }
    
    fn supports_extension(&self, ext: &str) -> bool {
        ext.eq_ignore_ascii_case("png")
    }
    
    fn format_name(&self) -> &'static str {
        "PNG"
    }
    
    fn supported_format_ids(&self) -> &[FormatID] {
        &[
            FormatID::PngRgb,
            FormatID::PngRgba,
            FormatID::PngPal,
            FormatID::PngPalMask,
        ]
    }
}

impl PngFormat {
    fn format_id_to_pixel_format(format_id: FormatID) -> PixelFormat {
        match format_id {
            FormatID::PngRgb => PixelFormat::Rgb,
            FormatID::PngRgba => PixelFormat::Rgba,
            FormatID::PngPal => PixelFormat::Indexed { 
                has_alpha: false, 
                bits_per_pixel: 8 
            },
            FormatID::PngPalMask => PixelFormat::Indexed { 
                has_alpha: true, 
                bits_per_pixel: 8 
            },
            _ => PixelFormat::Rgba, // 默认
        }
    }
    
    fn pixel_format_to_format_id(pixel_format: &PixelFormat) -> FormatID {
        match pixel_format {
            PixelFormat::Rgb => FormatID::PngRgb,
            PixelFormat::Rgba => FormatID::PngRgba,
            PixelFormat::Indexed { has_alpha: false, .. } => FormatID::PngPal,
            PixelFormat::Indexed { has_alpha: true, .. } => FormatID::PngPalMask,
            PixelFormat::Compressed { has_alpha: false, .. } => FormatID::PngRgb,
            PixelFormat::Compressed { has_alpha: true, .. } => FormatID::PngRgba,
        }
    }
}

/// 格式注册表 - 管理所有可用的格式处理器
pub struct FormatRegistry {
    formats: std::collections::HashMap<String, Box<dyn ImageFormat>>,
}

impl FormatRegistry {
    pub fn new() -> Self {
        let mut registry = Self {
            formats: std::collections::HashMap::new(),
        };
        
        // 注册默认格式
        registry.register("blp", Box::new(BlpFormat));
        registry.register("png", Box::new(PngFormat));
        
        registry
    }
    
    pub fn register(&mut self, extension: &str, format: Box<dyn ImageFormat>) {
        self.formats.insert(extension.to_lowercase(), format);
    }
    
    pub fn get_format(&self, extension: &str) -> Option<&dyn ImageFormat> {
        self.formats.get(&extension.to_lowercase()).map(|f| f.as_ref())
    }
    
    pub fn supported_extensions(&self) -> Vec<String> {
        self.formats.keys().cloned().collect()
    }
}
