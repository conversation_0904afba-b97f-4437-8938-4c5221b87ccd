fn main() {
    cc::Build::new()
        .cpp(true)
        .files([
            "../squish-1.11/alpha.cpp",
            "../squish-1.11/clusterfit.cpp",
            "../squish-1.11/colourblock.cpp",
            "../squish-1.11/colourfit.cpp",
            "../squish-1.11/colourset.cpp",
            "../squish-1.11/maths.cpp",
            "../squish-1.11/rangefit.cpp",
            "../squish-1.11/singlecolourfit.cpp",
            "../squish-1.11/squish.cpp",
            "squish_wrapper.cpp",
        ])
        .include("../squish-1.11")
        .flag("/std:c++20")
        .flag("/Ob2")
        .flag("/DNDEBUG")
        .flag("/GL")
        .flag("/Oy")
        .flag("/Ot")
        .flag("/Gy")
        .flag("/Zi")
        .flag("/fp:precise")
        .warnings(false)
        .compile("squish");

    println!("cargo:rustc-link-lib=static=squish");
    println!("cargo:rustc-link-search=native={}", std::env::var("OUT_DIR").unwrap());
}
