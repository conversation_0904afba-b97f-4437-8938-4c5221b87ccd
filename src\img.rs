use crate::blp;
use crate::png;
use imagequant::{Attributes as Attr, RGBA};
use std::{sync::Arc, thread, path::Path};
use crate::cfg::{FormatID, AppError};

fn offset_rgb(x: usize, y: usize, w: usize, h: usize, c: usize, bpp: usize) -> usize {
    let x = x.min(w - 1);
    let y = y.min(h - 1);
    (y * w + x) * bpp + c
}

#[derive(Default)]
pub struct MemImage {
    pub m_buffer: Vec<u8>,
    pub has_alpha: bool,
    pub is_palettized: bool,
    pub m_width: u32,
    pub m_height: u32,
    pub m_palette: Option<Vec<[u8; 3]>>,
}

impl MemImage { 
    pub fn build_mipmaps(self, mip_count: u32) -> Vec<Self> {
        if mip_count <= 1 { // no_mips现在由调用者控制
            return vec![self];
        }
        //println!("🚀 Optimierte parallele MIP-Generierung (<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>-Muster)...");
        let levels: Vec<u32> = (1..mip_count).collect();
        //println!("  - Plane, {} neue MIP-Level zu generieren.", levels.len());
    
        let shared_mip = Arc::new(self);
        
        let mut handles = vec![];
    
        for level in levels {
            let source = Arc::clone(&shared_mip);
    
            let handle = thread::spawn(move || {
                Self::build_mipmap(source, level)
            });

            handles.push(handle);
        }
        //println!("  - {} Worker-Threads wurden gestartet.", handles.len());
    
        //println!("  - Hauptthread wartet auf den Abschluss der Worker-Threads...");
        let mut final_mips: Vec<Self> = Vec::with_capacity(handles.len() + 1);
        for handle in handles {
            let result = handle.join().unwrap(); // `join()` gibt jetzt das `Self`-Objekt zurück
            final_mips.push(result);
        }
        //println!("✅ Alle Worker-Threads sind fertig."); 
    
        let mip0 = Arc::try_unwrap(shared_mip).unwrap_or_else(|_| panic!("Arc should have only one owner left"));
        final_mips.push(mip0);
        // let mut final_mips = vec![Arc::try_unwrap(shared_mip).unwrap_or_else(|_| panic!("Arc should have only one owner left"))];
        // final_mips.append(&mut generated_mips);
        
        final_mips.sort_unstable_by_key(|mip| mip.m_width.max(mip.m_height)); // Annahme: m_level existiert
        final_mips.reverse();
        //println!("✅ Ergebnisse sortiert und integriert. Gesamtanzahl der Mip-Level: {}", level_count);
        final_mips
    }  
    
    pub fn load_from_blp(&mut self, filename: &Path) -> Result<FormatID, AppError> {
        let blp_type = blp::load_from_blp(self, filename)?;
        Ok(blp_type)
    }

    pub fn load_from_png(&mut self, filename: &Path) -> Result<FormatID, AppError> {
        let png_type = png::load_from_png(self, filename)?;
        Ok(png_type)
    }

    pub fn save(self, filename: &Path, file_type: FormatID) -> Result<(), AppError> {
        if file_type.is_blp() {
            blp::save_to_blp(self, filename, file_type)?;
        } else  {
            png::save_to_png(self, filename, file_type)?;
        }

        Ok(())
    }

    pub fn palettize(&mut self) -> Result<(), AppError> {
        let bpp = if self.has_alpha {4} else {3};

        let width = self.m_width as usize;
        let height = self.m_height as usize;
        let pixel_count = width * height;

        // Pre-allocate with exact capacity and use unsafe for better performance
        let mut rgba = Vec::with_capacity(pixel_count);
        for px in self.m_buffer.chunks_exact(bpp) {
            rgba.push(RGBA {
                r: px[0],
                g: px[1],
                b: px[2],
                a: *px.get(3).unwrap_or(&255),
            });
        }

        let mut attr = Attr::new();
        attr.set_max_colors(256)?;

        let mut img = attr.new_image(rgba, width, height, 0.0)?;
        let mut res = attr.quantize(&mut img)?;

        let mut palette = Vec::with_capacity(256);
        for color in res.palette() {
            palette.push([color.r, color.g, color.b]);
        }

        if palette.len() < 256 {
            palette.resize(256, [0; 3]);
        }

        let (_, indices) = res.remapped(&mut img)?;

        // Optimize buffer operations to reduce allocations
        if self.has_alpha {
            // Pre-allocate the final buffer size
            let final_size = pixel_count * 2;
            let mut new_buffer = Vec::with_capacity(final_size);

            // Copy indices first
            new_buffer.extend(indices);

            // Copy alpha values directly
            for i in 0..pixel_count {
                new_buffer.push(self.m_buffer[i * 4 + 3]);
            }

            self.m_buffer = new_buffer;
        } else {
            self.m_buffer = indices;
        }

        self.m_palette = Some(palette);

        self.is_palettized = true;

        Ok(())
    }

    pub fn depalettize(&mut self) -> Result<(), AppError> {

        let bpp = if self.has_alpha {4} else {3};

        let pixel_count = (self.m_width * self.m_height) as usize;
        let alpha = self.m_buffer.split_off(pixel_count);

        let palette = self.m_palette.take().unwrap();

        // Optimize: directly write to final buffer without intermediate RGB vector
        let mut buffer = Vec::with_capacity(pixel_count * bpp);

        if !self.has_alpha {
            for &idx in &self.m_buffer {
                let [r, g, b] = palette[idx as usize];
                buffer.extend_from_slice(&[r, g, b]);
            }
        } else {
            for (i, &idx) in self.m_buffer.iter().enumerate() {
                let [r, g, b] = palette[idx as usize];
                buffer.extend_from_slice(&[r, g, b, alpha[i]]);
            }
        }

        self.m_buffer = buffer;
        self.is_palettized = false;

        Ok(())
    }

    pub fn remove_alpha(&mut self) -> Result<(), AppError> {

        let pixel_count = (self.m_width * self.m_height) as usize;

        if self.is_palettized {
            // For palettized images, just truncate to remove alpha data
            self.m_buffer.truncate(pixel_count);
        } else {
            // For RGB images, remove every 4th byte (alpha channel)
            let mut write_idx = 0;
            for read_idx in (0..self.m_buffer.len()).step_by(4) {
                if read_idx + 2 < self.m_buffer.len() {
                    self.m_buffer[write_idx] = self.m_buffer[read_idx];
                    self.m_buffer[write_idx + 1] = self.m_buffer[read_idx + 1];
                    self.m_buffer[write_idx + 2] = self.m_buffer[read_idx + 2];
                    write_idx += 3;
                }
            }
            self.m_buffer.truncate(write_idx);
        }

        self.has_alpha = false;
        Ok(())
    }

    pub fn add_alpha(&mut self) -> Result<(), AppError> {

        let pixel_count = (self.m_width * self.m_height) as usize;

        if self.is_palettized {
            // For palettized images, append alpha data (all opaque)
            self.m_buffer.resize(pixel_count * 2, 0xff);
        } else {
            // For RGB images, expand in-place from back to front to avoid overwriting
            let old_len = self.m_buffer.len();
            self.m_buffer.resize(pixel_count * 4, 0xff);

            // Work backwards to avoid overwriting data
            for i in (0..pixel_count).rev() {
                let src_idx = i * 3;
                let dst_idx = i * 4;
                if src_idx + 2 < old_len {
                    self.m_buffer[dst_idx + 2] = self.m_buffer[src_idx + 2]; // B
                    self.m_buffer[dst_idx + 1] = self.m_buffer[src_idx + 1]; // G
                    self.m_buffer[dst_idx] = self.m_buffer[src_idx];         // R
                    // Alpha is already set to 0xff by resize
                }
            }
        }

        self.has_alpha = true;
        Ok(())
    }
        
    pub fn build_mipmap(source: Arc<Self>, level: u32) -> Self {
        let mut mip = Self::default();
        mip.has_alpha = source.has_alpha;
        mip.is_palettized = source.is_palettized;
        mip.m_width = (source.m_width >> level).max(1);
        mip.m_height = (source.m_height >> level).max(1);
        let step: usize = 1 << level;
        let bpp: u32 = if mip.is_palettized {1} else if mip.has_alpha {4} else {3};
        let dst_size = (mip.m_width * mip.m_height * bpp + if mip.has_alpha && mip.is_palettized {mip.m_width * mip.m_height} else {0}) as usize;

        mip.m_buffer.resize(dst_size as usize, 0);

        if mip.is_palettized {
            Self::process_palettized_pixel_from_base_global_palette_fit(
                &source.m_buffer,
                &mut mip.m_buffer,
                &source.m_palette,
                source.m_width as usize,
                source.m_height as usize,
                mip.m_width as usize,
                mip.m_height as usize,
                step,
                mip.has_alpha,
            );
        } else {
            Self::process_rgb_pixel_from_base(
                &source.m_buffer,
                &mut mip.m_buffer,
                bpp as usize,
                source.m_width as usize,
                source.m_height as usize,
                mip.m_width as usize,
                mip.m_height as usize,
                step,
                mip.has_alpha,
            );
        };

        mip
    }

    fn process_rgb_pixel_from_base(
        src: &[u8],
        dst: &mut [u8],
        bpp: usize,
        base_w: usize,
        base_h: usize,
        dst_w: usize,
        dst_h: usize,
        step: usize,
        has_alpha: bool,
    ) {
        for j in 0..dst_h {
            for i in 0..dst_w {
                // === 优化：一次性收集所有样本数据 ===
                let max_samples = step * step;
                let mut samples_data = Vec::with_capacity(max_samples * bpp);
                let mut alphas = Vec::with_capacity(max_samples);

                for oy in 0..step {
                    for ox in 0..step {
                        let sx = i * step + ox;
                        let sy = j * step + oy;
                        if sx < base_w && sy < base_h {
                            // 收集所有通道的数据
                            for c in 0..bpp {
                                samples_data.push(src[offset_rgb(sx, sy, base_w, base_h, c, bpp)]);
                            }

                            let a = if has_alpha {
                                src[offset_rgb(sx, sy, base_w, base_h, 3, bpp)] as f32 / 255.0
                            } else {
                                1.0
                            };
                            alphas.push(a);
                        }
                    }
                }

                // === 计算权重 ===
                let total_alpha: f32 = alphas.iter().sum();
                let uniform_weight = 1.0 / alphas.len() as f32;

                // === 对每个通道做加权平均 ===
                for c in 0..bpp {
                    let mut weighted_sum = 0.0;
                    for (idx, &alpha) in alphas.iter().enumerate() {
                        let weight = if has_alpha && total_alpha > 0.01 {
                            alpha / total_alpha
                        } else {
                            uniform_weight
                        };
                        let val = samples_data[idx * bpp + c] as f32;
                        weighted_sum += val * weight;
                    }

                    dst[offset_rgb(i, j, dst_w, dst_h, c, bpp)] = weighted_sum.clamp(0.0, 255.0).round() as u8;
                }
            }
        }
    }    

    fn process_palettized_pixel_from_base_global_palette_fit(
        src: &[u8],
        dst: &mut [u8],
        palette: &Option<Vec<[u8; 3]>>,
        base_w: usize,
        base_h: usize,
        dst_w: usize,
        dst_h: usize,
        step: usize,
        has_alpha: bool,
    ) {
        let palette = palette.as_ref();
    
        for j in 0..dst_h {
            for i in 0..dst_w {
                let max_samples = step * step;
                let mut alphas = Vec::with_capacity(max_samples);
                let mut colors = Vec::with_capacity(max_samples);

                for oy in 0..step {
                    for ox in 0..step {
                        let sx = i * step + ox;
                        let sy = j * step + oy;
                        if sx < base_w && sy < base_h {
                            let idx = offset_rgb(sx, sy, base_w, base_h, 0, 1);
                            let pal_ix = src[idx] as usize;
                            let rgb = palette.unwrap()[pal_ix];
                            colors.push((rgb[0], rgb[1], rgb[2]));
                            if has_alpha {
                                let alpha_idx = base_w * base_h + idx;
                                alphas.push(src[alpha_idx] as f32 / 255.0);
                            } else {
                                alphas.push(1.0);
                            }
                        }
                    }
                }
    
                // === 权重 ===
                let total_alpha: f32 = if has_alpha { alphas.iter().sum() } else { 1.0 };
                let weights: Vec<f32> = if has_alpha && total_alpha > 0.01 {
                    alphas.iter().map(|&a| a / total_alpha).collect()
                } else {
                    vec![1.0 / alphas.len() as f32; alphas.len()]
                };
    
                // === 加权平均颜色 ===
                let (r, g, b): (f32, f32, f32) = colors.iter().zip(weights.iter()).fold(
                    (0.0, 0.0, 0.0),
                    |(r, g, b), ((cr, cg, cb), w)| {
                        (r + *cr as f32 * w, g + *cg as f32 * w, b + *cb as f32 * w)
                    },
                );
    
                let best_ix = palette
                    .unwrap()
                    .iter()
                    .enumerate()
                    .min_by_key(|&(_, rgb)| {
                        let dr = rgb[0] as f32 - r;
                        let dg = rgb[1] as f32 - g;
                        let db = rgb[2] as f32 - b;
                        (dr.abs() + dg.abs() + db.abs()) as usize
                    })
                    .map(|(ix, _)| ix)
                    .unwrap_or(0);

                // === 写入结果 ===
                dst[offset_rgb(i, j, dst_w, dst_h, 0, 1)] = best_ix as u8;
    
                if has_alpha {
                    let avg_alpha = ((total_alpha / alphas.len() as f32) * 255.0).round().clamp(0.0, 255.0);
                    dst[dst_w * dst_h + offset_rgb(i, j, dst_w, dst_h, 0, 1)] = avg_alpha as u8;
                }
            }
        }
    }
}