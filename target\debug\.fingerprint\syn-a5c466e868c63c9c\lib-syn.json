{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 11878913646895990253, "deps": [[1988483478007900009, "unicode_ident", false, 14876886335934717084], [3060637413840920116, "proc_macro2", false, 15345866277712466708], [17990358020177143287, "quote", false, 13476667981894634916]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-a5c466e868c63c9c\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}