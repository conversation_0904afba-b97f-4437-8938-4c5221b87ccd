{"rustc": 16591470773350601817, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 9982029754039101705, "deps": [[4675849561795547236, "miniz_oxide", false, 8225642350851348995], [5466618496199522463, "crc32fast", false, 13646148400790068431]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-9803b98dd56868e7\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}